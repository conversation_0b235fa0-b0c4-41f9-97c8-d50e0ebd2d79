<ng-container *ngIf="cipher.type === CipherType.Login">
  <ng-container *ngIf="showQuickCopyActions$ | async; else loginCopyMenu">
    <bit-item-action>
      <button
        type="button"
        bitIconButton="bwi-user"
        size="small"
        appCopyField="username"
        [cipher]="cipher"
        [appA11yTitle]="'copyUsername' | i18n"
      ></button>
    </bit-item-action>
    <bit-item-action>
      <button
        *ngIf="cipher.viewPassword"
        type="button"
        bitIconButton="bwi-key"
        size="small"
        appCopyField="password"
        [cipher]="cipher"
        [appA11yTitle]="'copyPassword' | i18n"
      ></button>
    </bit-item-action>
    <bit-item-action>
      <button
        type="button"
        bitIconButton="bwi-clock"
        size="small"
        appCopyField="totp"
        [cipher]="cipher"
        [appA11yTitle]="'copyVerificationCode' | i18n"
      ></button>
    </bit-item-action>
  </ng-container>

  <ng-template #loginCopyMenu>
    <bit-item-action>
      <button
        *ngIf="singleCopiableLogin"
        type="button"
        bitIconButton="bwi-clone"
        size="small"
        [appA11yTitle]="singleCopiableLogin.key"
        [appCopyField]="$any(singleCopiableLogin.field)"
        [cipher]="cipher"
      ></button>
      <ng-container *ngIf="!singleCopiableLogin">
        <button
          type="button"
          bitIconButton="bwi-clone"
          size="small"
          [appA11yTitle]="
            hasLoginValues ? ('copyInfoTitle' | i18n: cipher.name) : ('noValuesToCopy' | i18n)
          "
          [disabled]="!hasLoginValues"
          [bitMenuTriggerFor]="loginOptions"
        ></button>
        <bit-menu #loginOptions>
          <button type="button" bitMenuItem appCopyField="username" [cipher]="cipher">
            {{ "copyUsername" | i18n }}
          </button>
          <button
            *ngIf="cipher.viewPassword"
            type="button"
            bitMenuItem
            appCopyField="password"
            [cipher]="cipher"
          >
            {{ "copyPassword" | i18n }}
          </button>
          <button type="button" bitMenuItem appCopyField="totp" [cipher]="cipher">
            {{ "copyVerificationCode" | i18n }}
          </button>
        </bit-menu>
      </ng-container>
    </bit-item-action>
  </ng-template>
</ng-container>

<ng-container *ngIf="cipher.type === CipherType.Card">
  <ng-container *ngIf="showQuickCopyActions$ | async; else cardCopyMenu">
    <bit-item-action>
      <button
        type="button"
        bitIconButton="bwi-hashtag"
        size="small"
        appCopyField="cardNumber"
        [cipher]="cipher"
        [appA11yTitle]="'copyNumber' | i18n"
      ></button>
    </bit-item-action>
    <bit-item-action>
      <button
        type="button"
        bitIconButton="bwi-key"
        size="small"
        appCopyField="securityCode"
        [cipher]="cipher"
        [appA11yTitle]="'copySecurityCode' | i18n"
      ></button>
    </bit-item-action>
  </ng-container>
  <ng-template #cardCopyMenu>
    <bit-item-action>
      <button
        *ngIf="singleCopiableCard"
        type="button"
        bitIconButton="bwi-clone"
        size="small"
        [appA11yTitle]="'copyFieldValue' | i18n: singleCopiableCard.key : singleCopiableCard.value"
        [appCopyClick]="singleCopiableCard.value"
        [valueLabel]="singleCopiableCard.key"
        showToast
      ></button>
      <ng-container *ngIf="!singleCopiableCard">
        <button
          type="button"
          bitIconButton="bwi-clone"
          size="small"
          [appA11yTitle]="
            hasCardValues ? ('copyInfoTitle' | i18n: cipher.name) : ('noValuesToCopy' | i18n)
          "
          [disabled]="!hasCardValues"
          [bitMenuTriggerFor]="cardOptions"
        ></button>
        <bit-menu #cardOptions>
          <button type="button" bitMenuItem appCopyField="cardNumber" [cipher]="cipher">
            {{ "copyNumber" | i18n }}
          </button>
          <button type="button" bitMenuItem appCopyField="securityCode" [cipher]="cipher">
            {{ "copySecurityCode" | i18n }}
          </button>
        </bit-menu>
      </ng-container>
    </bit-item-action>
  </ng-template>
</ng-container>

<bit-item-action *ngIf="cipher.type === CipherType.Identity">
  <button
    *ngIf="singleCopiableIdentity"
    type="button"
    bitIconButton="bwi-clone"
    size="small"
    [appA11yTitle]="
      'copyFieldValue' | i18n: singleCopiableIdentity.key : singleCopiableIdentity.value
    "
    [appCopyClick]="singleCopiableIdentity.value"
    [valueLabel]="singleCopiableIdentity.key"
    showToast
  ></button>
  <ng-container *ngIf="!singleCopiableIdentity">
    <button
      type="button"
      bitIconButton="bwi-clone"
      size="small"
      [appA11yTitle]="
        hasIdentityValues ? ('copyInfoTitle' | i18n: cipher.name) : ('noValuesToCopy' | i18n)
      "
      [disabled]="!hasIdentityValues"
      [bitMenuTriggerFor]="identityOptions"
    ></button>
    <bit-menu #identityOptions>
      <button type="button" bitMenuItem appCopyField="username" [cipher]="cipher">
        {{ "copyUsername" | i18n }}
      </button>
      <button type="button" bitMenuItem appCopyField="email" [cipher]="cipher">
        {{ "copyEmail" | i18n }}
      </button>
      <button type="button" bitMenuItem appCopyField="phone" [cipher]="cipher">
        {{ "copyPhone" | i18n }}
      </button>
      <button type="button" bitMenuItem appCopyField="address" [cipher]="cipher">
        {{ "copyAddress" | i18n }}
      </button>
    </bit-menu>
  </ng-container>
</bit-item-action>

<bit-item-action *ngIf="cipher.type === CipherType.SecureNote">
  <button
    type="button"
    bitIconButton="bwi-clone"
    size="small"
    [appA11yTitle]="
      hasSecureNoteValue ? ('copyNoteTitle' | i18n: cipher.name) : ('noValuesToCopy' | i18n)
    "
    appCopyField="secureNote"
    [cipher]="cipher"
  ></button>
</bit-item-action>

<bit-item-action *ngIf="cipher.type === CipherType.SshKey">
  <button
    type="button"
    bitIconButton="bwi-clone"
    size="small"
    [appA11yTitle]="
      hasSshKeyValues ? ('copyInfoTitle' | i18n: cipher.name) : ('noValuesToCopy' | i18n)
    "
    [disabled]="!hasSshKeyValues"
    [bitMenuTriggerFor]="sshKeyOptions"
  ></button>
  <bit-menu #sshKeyOptions>
    <button type="button" bitMenuItem appCopyField="privateKey" [cipher]="cipher">
      {{ "copyPrivateKey" | i18n }}
    </button>
    <button type="button" bitMenuItem appCopyField="publicKey" [cipher]="cipher">
      {{ "copyPublicKey" | i18n }}
    </button>
    <button type="button" bitMenuItem appCopyField="keyFingerprint" [cipher]="cipher">
      {{ "copyFingerprint" | i18n }}
    </button>
  </bit-menu>
</bit-item-action>
