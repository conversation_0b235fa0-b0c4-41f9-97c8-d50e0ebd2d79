<popup-page>
  <popup-header slot="header" pageTitle="{{ 'autofill' | i18n }}" showBackButton>
    <ng-container slot="end">
      <app-pop-out></app-pop-out>
    </ng-container>
  </popup-header>

  <div class="tw-bg-background-alt">
    <div *ngIf="!defaultBrowserAutofillDisabled && (showSpotlightNudge$ | async)" class="tw-mb-6">
      <bit-spotlight
        [title]="'autofillSpotlightTitle' | i18n"
        [subtitle]="'autofillSpotlightDesc' | i18n"
        [buttonText]="spotlightButtonText"
        (onDismiss)="dismissSpotlight()"
        (onButtonClick)="openURI($event, disablePasswordManagerURI)"
        [buttonIcon]="spotlightButtonIcon"
      ></bit-spotlight>
    </div>
    <bit-section>
      <bit-section-header>
        <h2 bitTypography="h6">{{ "autofillSuggestionsSectionTitle" | i18n }}</h2>
      </bit-section-header>
      <bit-card>
        <bit-form-control [disableMargin]="!enableInlineMenu && !canOverrideBrowserAutofillSetting">
          <input
            bitCheckbox
            id="show-inline-menu"
            type="checkbox"
            (change)="updateInlineMenuVisibility()"
            [(ngModel)]="enableInlineMenu"
          />
          <bit-label for="show-inline-menu">{{ "showInlineMenuLabel" | i18n }}</bit-label>
          <bit-hint
            *ngIf="accountSwitcherEnabled && canOverrideBrowserAutofillSetting"
            class="tw-text-sm"
          >
            {{ "showInlineMenuOnFormFieldsDescAlt" | i18n }}
          </bit-hint>
          <bit-hint
            *ngIf="!canOverrideBrowserAutofillSetting"
            [class]="!enableInlineMenu ? 'tw-text-sm tw-mb-6' : 'tw-text-sm'"
          >
            {{ "turnOffBrowserBuiltInPasswordManagerSettings" | i18n }}
            <a
              bitLink
              class="tw-no-underline"
              rel="noreferrer"
              target="_blank"
              (click)="openURI($event, disablePasswordManagerURI)"
              [attr.href]="disablePasswordManagerURI"
            >
              {{ "turnOffBrowserBuiltInPasswordManagerSettingsLink" | i18n }}
            </a>
          </bit-hint>
        </bit-form-control>
        <bit-form-control *ngIf="enableInlineMenu" class="tw-ml-5">
          <input
            bitCheckbox
            id="show-inline-menu-identities"
            type="checkbox"
            (change)="updateShowInlineMenuIdentities()"
            [(ngModel)]="showInlineMenuIdentities"
          />
          <bit-label for="show-inline-menu-identities">
            {{ "showInlineMenuIdentitiesLabel" | i18n }}
          </bit-label>
        </bit-form-control>
        <bit-form-control *ngIf="enableInlineMenu" class="tw-ml-5">
          <input
            bitCheckbox
            id="show-inline-menu-cards"
            type="checkbox"
            (change)="updateShowInlineMenuCards()"
            [(ngModel)]="showInlineMenuCards"
          />
          <bit-label for="show-inline-menu-cards">
            {{ "showInlineMenuCardsLabel" | i18n }}
          </bit-label>
        </bit-form-control>
        <bit-form-control *ngIf="enableInlineMenu" class="tw-ml-5">
          <input
            bitCheckbox
            id="show-autofill-suggestions-on-icon"
            type="checkbox"
            (change)="updateInlineMenuVisibility()"
            [(ngModel)]="enableInlineMenuOnIconSelect"
          />
          <bit-label for="show-autofill-suggestions-on-icon">
            {{ "showInlineMenuOnIconSelectionLabel" | i18n }}
          </bit-label>
        </bit-form-control>
        <bit-form-control *ngIf="canOverrideBrowserAutofillSetting">
          <input
            bitCheckbox
            id="overrideBrowserAutofill"
            type="checkbox"
            (change)="updateDefaultBrowserAutofillDisabled()"
            [(ngModel)]="defaultBrowserAutofillDisabled"
          />
          <bit-label for="overrideBrowserAutofill">{{
            "overrideDefaultBrowserAutoFillSettings" | i18n
          }}</bit-label>
          <bit-hint class="tw-text-sm">
            {{ "turnOffBrowserBuiltInPasswordManagerSettings" | i18n }}
            <a
              bitLink
              class="tw-no-underline"
              rel="noreferrer"
              target="_blank"
              (click)="openURI($event, disablePasswordManagerURI)"
              [attr.href]="disablePasswordManagerURI"
            >
              {{ "turnOffBrowserBuiltInPasswordManagerSettingsLink" | i18n }}
            </a>
          </bit-hint>
        </bit-form-control>
        <bit-form-control>
          <input
            bitCheckbox
            id="showCardsSuggestions"
            type="checkbox"
            (change)="updateShowCardsCurrentTab()"
            [(ngModel)]="showCardsCurrentTab"
          />
          <bit-label for="showCardsSuggestions">{{ "showCardsInVaultViewV2" | i18n }}</bit-label>
        </bit-form-control>
        <bit-form-control disableMargin>
          <input
            bitCheckbox
            id="showIdentitiesSuggestions"
            type="checkbox"
            (change)="updateShowIdentitiesCurrentTab()"
            [(ngModel)]="showIdentitiesCurrentTab"
          />
          <bit-label for="showIdentitiesSuggestions" class="tw-whitespace-normal">
            {{ "showIdentitiesInVaultViewV2" | i18n }}
          </bit-label>
        </bit-form-control>
      </bit-card>
    </bit-section>
    <bit-section>
      <bit-section-header>
        <h2 bitTypography="h6">{{ "autofillKeyboardShortcutSectionTitle" | i18n }}</h2>
      </bit-section-header>
      <bit-item>
        <button bit-item-content type="button" (click)="openURI($event, browserShortcutsURI)">
          <h3 bitTypography="body2">{{ "autofillKeyboardManagerShortcutsLabel" | i18n }}</h3>
          <bit-hint slot="secondary" class="tw-text-sm tw-whitespace-normal">
            {{ autofillKeyboardHelperText }}
          </bit-hint>
          <i
            appA11yTitle="{{ 'opensInANewWindow' | i18n }}"
            aria-hidden="true"
            class="bwi bwi-fw bwi-external-link bwi-lg tw-text-muted"
            slot="end"
          ></i>
        </button>
      </bit-item>
    </bit-section>
    <bit-section>
      <form [formGroup]="autofillOnPageLoadForm">
        <bit-section-header>
          <legend>
            <h2 bitTypography="h6">{{ "enableAutoFillOnPageLoadSectionTitle" | i18n }}</h2>
          </legend>
        </bit-section-header>
        <bit-card>
          <bit-hint class="tw-mb-6 tw-text-sm">
            {{ "enableAutoFillOnPageLoadDesc" | i18n }}
            <span
              ><b>{{ "warningCapitalized" | i18n }}</b
              >: {{ "experimentalFeature" | i18n }}</span
            >
            <a
              bitLink
              class="tw-no-underline"
              href="https://bitwarden.com/help/auto-fill-browser/"
              rel="noreferrer"
              target="_blank"
            >
              {{ "learnMoreAboutAutofillOnPageLoadLinkText" | i18n }}
            </a>
          </bit-hint>
          <bit-form-control>
            <input
              formControlName="autofillOnPageLoad"
              bitCheckbox
              id="autofillOnPageLoad"
              type="checkbox"
            />
            <bit-label for="autofillOnPageLoad">{{ "enableAutoFillOnPageLoad" | i18n }}</bit-label>
            <bit-hint class="tw-text-sm" *ngIf="autofillOnPageLoadFromPolicy$ | async">{{
              "enterprisePolicyRequirementsApplied" | i18n
            }}</bit-hint>
          </bit-form-control>
          <bit-form-field disableMargin>
            <bit-label for="defaultAutofill">{{ "defaultAutoFillOnPageLoad" | i18n }}</bit-label>
            <bit-select formControlName="defaultAutofill" bitInput id="defaultAutofill">
              <bit-option
                *ngFor="let option of autofillOnPageLoadOptions"
                [label]="option.name"
                [value]="option.value"
              >
              </bit-option>
            </bit-select>
            <bit-hint class="tw-text-sm">
              {{ "defaultAutoFillOnPageLoadDesc" | i18n }}
            </bit-hint>
          </bit-form-field>
        </bit-card>
      </form>
    </bit-section>
    <bit-section [disableMargin]="!blockBrowserInjectionsByDomainEnabled">
      <form [formGroup]="additionalOptionsForm">
        <bit-section-header>
          <h2 bitTypography="h6">{{ "additionalOptions" | i18n }}</h2>
        </bit-section-header>
        <bit-card>
          <bit-form-control>
            <input
              formControlName="enableContextMenuItem"
              bitCheckbox
              id="context-menu"
              type="checkbox"
            />
            <bit-label for="context-menu">{{ "enableContextMenuItem" | i18n }}</bit-label>
          </bit-form-control>
          <bit-form-control>
            <input formControlName="enableAutoTotpCopy" bitCheckbox id="totp" type="checkbox" />
            <bit-label for="totp">{{ "enableAutoTotpCopy" | i18n }}</bit-label>
          </bit-form-control>
          <bit-form-field>
            <bit-label for="clearClipboard">{{ "clearClipboard" | i18n }}</bit-label>
            <bit-select
              formControlName="clearClipboard"
              aria-describedby="clearClipboardHelp"
              bitInput
              id="clearClipboard"
            >
              <bit-option
                *ngFor="let option of clearClipboardOptions"
                [label]="option.name"
                [value]="option.value"
              ></bit-option>
            </bit-select>
            <bit-hint class="tw-text-sm" id="clearClipboardHelp">
              {{ "clearClipboardDesc" | i18n }}
            </bit-hint>
          </bit-form-field>
          <bit-form-field disableMargin>
            <bit-label for="defaultUriMatch">{{ "defaultUriMatchDetection" | i18n }}</bit-label>
            <bit-select
              formControlName="defaultUriMatch"
              aria-describedby="defaultUriMatchHelp"
              bitInput
              id="defaultUriMatch"
            >
              <bit-option
                *ngFor="let option of uriMatchOptions"
                [label]="option.name"
                [value]="option.value"
              ></bit-option>
            </bit-select>
            <bit-hint class="tw-text-sm" id="defaultUriMatchHelp">
              {{ "defaultUriMatchDetectionDesc" | i18n }}
            </bit-hint>
          </bit-form-field>
        </bit-card>
      </form>
    </bit-section>
    <bit-section *ngIf="blockBrowserInjectionsByDomainEnabled" disableMargin>
      <bit-item>
        <a bit-item-content routerLink="/blocked-domains">{{ "blockedDomains" | i18n }}</a>
        <i slot="end" class="bwi bwi-angle-right bwi-lg row-sub-icon" aria-hidden="true"></i>
      </bit-item>
    </bit-section>
  </div>
</popup-page>
