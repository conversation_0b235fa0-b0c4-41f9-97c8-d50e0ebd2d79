<section class="tw-mb-5 bit-compact:tw-mb-4">
  <bit-section-header>
    <h2 bitTypography="h6">{{ "loginCredentials" | i18n }}</h2>
  </bit-section-header>
  <read-only-cipher-card>
    <bit-form-field *ngIf="cipher.login.username">
      <bit-label [appTextDrag]="cipher.login.username">
        {{ "username" | i18n }}
      </bit-label>
      <input
        id="userName"
        readonly
        bitInput
        type="text"
        [value]="cipher.login.username"
        aria-readonly="true"
        data-testid="login-username"
      />
      <button
        bitIconButton="bwi-clone"
        bitSuffix
        type="button"
        [appCopyClick]="cipher.login.username"
        [valueLabel]="'username' | i18n"
        showToast
        [appA11yTitle]="'copyUsername' | i18n"
        data-testid="copy-username"
      ></button>
    </bit-form-field>
    <bit-form-field *ngIf="cipher.login.password">
      <bit-label [appTextDrag]="cipher.login.password" id="password-label">
        {{ "password" | i18n }}
      </bit-label>
      <input
        id="password"
        [ngClass]="{ 'tw-hidden': passwordRevealed }"
        readonly
        bitInput
        type="password"
        [value]="cipher.login.password"
        aria-readonly="true"
        data-testid="login-password"
      />
      <!-- Use a wrapping span to "recreate" a readonly input as close as possible -->
      <span
        *ngIf="passwordRevealed"
        role="textbox"
        tabindex="0"
        data-testid="login-password-color"
        aria-readonly="true"
        [attr.aria-label]="cipher.login.password"
        aria-labelledby="password-label"
      >
        <bit-color-password
          class="tw-font-mono"
          [password]="cipher.login.password"
        ></bit-color-password>
      </span>
      <button
        *ngIf="cipher.viewPassword && passwordRevealed"
        bitIconButton="bwi-numbered-list"
        bitSuffix
        type="button"
        data-testid="toggle-password-count"
        [appA11yTitle]="(showPasswordCount ? 'hideCharacterCount' : 'showCharacterCount') | i18n"
        [attr.aria-expanded]="showPasswordCount"
        appStopClick
        (click)="togglePasswordCount()"
      ></button>
      <button
        *ngIf="cipher.viewPassword"
        bitSuffix
        type="button"
        bitIconButton
        bitPasswordInputToggle
        [toggled]="passwordRevealed"
        data-testid="toggle-password"
        (toggledChange)="pwToggleValue($event)"
      ></button>
      <button
        *ngIf="cipher.viewPassword"
        bitIconButton="bwi-clone"
        bitSuffix
        type="button"
        [appCopyClick]="cipher.login.password"
        [valueLabel]="'password' | i18n"
        showToast
        [appA11yTitle]="'copyPassword' | i18n"
        data-testid="copy-password"
        (click)="logCopyEvent()"
      ></button>
    </bit-form-field>
    <bit-hint *ngIf="hadPendingChangePasswordTask">
      <a bitLink href="#" appStopClick (click)="launchChangePasswordEvent()">
        {{ "changeAtRiskPassword" | i18n }}
        <i class="bwi bwi-external-link tw-ml-1" aria-hidden="true"></i>
      </a>
    </bit-hint>
    <div
      *ngIf="showPasswordCount && passwordRevealed"
      [ngClass]="{ 'tw-mt-3': !cipher.login.totp, 'tw-mb-2': true }"
    >
      <bit-color-password
        [password]="cipher.login.password"
        [showCount]="true"
      ></bit-color-password>
    </div>
    <bit-form-field *ngIf="cipher.login?.fido2Credentials?.length > 0">
      <bit-label [appTextDrag]="fido2CredentialCreationDateValue"
        >{{ "typePasskey" | i18n }}
      </bit-label>
      <input
        id="fido"
        readonly
        bitInput
        type="text"
        [value]="fido2CredentialCreationDateValue"
        aria-readonly="true"
        data-testid="login-passkey"
      />
    </bit-form-field>
    <bit-form-field *ngIf="cipher.login.totp">
      <bit-label [appTextDrag]="totpCodeCopyObj?.totpCode"
        >{{ "verificationCodeTotp" | i18n }}
        <!-- MODIFIED: Removed premium badge requirement -->
      </bit-label>
      <input
        id="totp"
        readonly
        bitInput
        type="text"
        [value]="totpCodeCopyObj?.totpCodeFormatted || '*** ***'"
        aria-readonly="true"
        data-testid="login-totp"
        class="tw-font-mono"
      />
      <div
        bitTotpCountdown
        [cipher]="cipher"
        bitSuffix
        (sendCopyCode)="setTotpCopyCode($event)"
      ></div>
      <button
        bitIconButton="bwi-clone"
        bitSuffix
        type="button"
        [appCopyClick]="totpCodeCopyObj?.totpCode"
        [valueLabel]="'verificationCodeTotp' | i18n"
        showToast
        [appA11yTitle]="'copyVerificationCode' | i18n"
        data-testid="copy-totp"
        class="disabled:tw-cursor-default"
      ></button>
    </bit-form-field>
  </read-only-cipher-card>
</section>
