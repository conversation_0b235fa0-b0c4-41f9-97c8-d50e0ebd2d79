{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "<PERSON><PERSON><PERSON><PERSON> Bitwarden", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, yn y gwei<PERSON><PERSON>, neu ar fynd, mae <PERSON> yn di<PERSON>u eich holl gyfrine<PERSON>au a gwybodaeth sensitif", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Mewngofnodwch neu crëwch gyfrif newydd i gael mynediad i'ch cell ddiogel."}, "inviteAccepted": {"message": "Invitation accepted"}, "createAccount": {"message": "<PERSON><PERSON><PERSON> cyfrif"}, "newToBitwarden": {"message": "Newydd i Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "<PERSON><PERSON><PERSON><PERSON> nô<PERSON>"}, "setAStrongPassword": {"message": "<PERSON><PERSON><PERSON> cyf<PERSON>ir cryf"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Finish creating your account by setting a password"}, "enterpriseSingleSignOn": {"message": "Enterprise single sign-on"}, "cancel": {"message": "Canslo"}, "close": {"message": "Cau"}, "submit": {"message": "Cyflwyno"}, "emailAddress": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "masterPass": {"message": "<PERSON><PERSON><PERSON>"}, "masterPassDesc": {"message": "Y prif gyfrinair yw'r cyfrinair ddefnyddiwch chi i gael mynediad i'ch cell. Mae'n bwysig iawn nad ydych chi'n anghofio eich prif gyfrinair. Does dim modd adfer y cyfrinair pe baech yn ei anghofio."}, "masterPassHintDesc": {"message": "Gall awgrym o'ch prif gyfrinair eich helpu i'w gofio os ydych chi'n ei anghofio."}, "masterPassHintText": {"message": "Os anghofiwch chi eich cyfrinair, gallwch anfon awgrym i'ch cyfeiriad ebost. $CURRENT$/$MAXIMUM$ nod.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Re-type master password"}, "masterPassHint": {"message": "Master password hint (optional)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Ymuno â $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "Tab"}, "vault": {"message": "Cell"}, "myVault": {"message": "<PERSON>y nghell"}, "allVaults": {"message": "Pob cell"}, "tools": {"message": "Offer"}, "settings": {"message": "Gosodiadau"}, "currentTab": {"message": "Y tab cyfredol"}, "copyPassword": {"message": "Co<PERSON><PERSON><PERSON> c<PERSON>"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yn"}, "copyUri": {"message": "Copïo URI"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON><PERSON> enw defnyddiwr"}, "copyNumber": {"message": "<PERSON><PERSON><PERSON><PERSON> rhif"}, "copySecurityCode": {"message": "Copy security code"}, "copyName": {"message": "Copy name"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copïo $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wefan"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Copy to clipboard"}, "fill": {"message": "<PERSON><PERSON><PERSON>", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> awtomatig"}, "autoFillLogin": {"message": "<PERSON><PERSON><PERSON> <PERSON> mewngofnodi"}, "autoFillCard": {"message": "<PERSON><PERSON><PERSON> cerdyn"}, "autoFillIdentity": {"message": "<PERSON><PERSON><PERSON>"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON> cyfrinair (wed<PERSON>'<PERSON> gopïo)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON><PERSON><PERSON> enw maes addasedig"}, "noMatchingLogins": {"message": "No matching logins"}, "noCards": {"message": "<PERSON><PERSON> cardiau"}, "noIdentities": {"message": "<PERSON>m eitemau huna<PERSON>"}, "addLoginMenu": {"message": "Ychwanegu manylion mewngofnodi"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cerdyn"}, "addIdentityMenu": {"message": "Ychwanegu eitem hunaniaeth"}, "unlockVaultMenu": {"message": "Datgloi'ch cell"}, "loginToVaultMenu": {"message": "Mewngofnodi i'ch cell"}, "autoFillInfo": {"message": "There are no logins available to autofill for the current browser tab."}, "addLogin": {"message": "Ychwanegu manylion mewngofnodi"}, "addItem": {"message": "Ychwanegu eitem"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON> awgrym o'ch prif gyf<PERSON>ir"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Anfon cod dilysu i'ch cyfeiriad ebost"}, "sendCode": {"message": "Anfod cod"}, "codeSent": {"message": "Cod wedi'i anfon"}, "verificationCode": {"message": "Cod dilysu"}, "confirmIdentity": {"message": "Cadarnhewch eich hunaniaeth i barhau."}, "changeMasterPassword": {"message": "<PERSON><PERSON> y prif gyfrinair"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Ymadrodd unigryw", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "<PERSON><PERSON><PERSON><PERSON> unigryw eich cyfrif", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Mewngofnodi dau gam"}, "logOut": {"message": "Allgofnodi"}, "aboutBitwarden": {"message": "Ynghylch Bitwarden"}, "about": {"message": "Ynghylch"}, "moreFromBitwarden": {"message": "Mwy gan Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Dilyswr Bitwarden"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "Fersiwn"}, "save": {"message": "Cadw"}, "move": {"message": "<PERSON><PERSON><PERSON>"}, "addFolder": {"message": "<PERSON>chwaneg<PERSON> ffolder"}, "name": {"message": "Enw"}, "editFolder": {"message": "<PERSON><PERSON><PERSON> ffolder"}, "editFolderWithName": {"message": "Golygu ffolder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "<PERSON><PERSON><PERSON> new<PERSON>d"}, "folderName": {"message": "Enw'r ffolder"}, "folderHintText": {"message": "Gallwch nythu ffolder drwy ychwanegu enw ffolder arall wedi'i olynu gan \"/\". Enghraifft: Cymdeithasol/Fforymau"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>er"}, "folders": {"message": "<PERSON><PERSON><PERSON>"}, "noFolders": {"message": "Does dim ffolderi i'w rhestru."}, "helpFeedback": {"message": "Cymorth ac adborth"}, "helpCenter": {"message": "<PERSON><PERSON><PERSON>"}, "communityForums": {"message": "Explore Bitwarden community forums"}, "contactSupport": {"message": "Contact Bitwarden support"}, "sync": {"message": "<PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "Cysoni'r gell nawr"}, "lastSync": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>:"}, "passGen": {"message": "Cynh<PERSON><PERSON><PERSON>"}, "generator": {"message": "Cynhyrchydd", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Cynhyrchu cyfrineiriau cryf ac unigryw ar gyfer eich cyfrifon yn awtomatig."}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Mewnforio e<PERSON>u"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "Cynhyrchu cyfrinair"}, "generatePassphrase": {"message": "Cynhyrchu cyf<PERSON>ymadrodd"}, "passwordGenerated": {"message": "<PERSON><PERSON><PERSON><PERSON> wedi'i gynhyrchu"}, "passphraseGenerated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wedi'i gynhyrchu"}, "usernameGenerated": {"message": "Enw defnyddiwr wedi'i gynhyrchu"}, "emailGenerated": {"message": "<PERSON><PERSON><PERSON><PERSON> e<PERSON> wedi'i gynhyrchu"}, "regeneratePassword": {"message": "Ailgynhyrchu cyfrinair"}, "options": {"message": "Dewisiadau"}, "length": {"message": "Hyd"}, "include": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Cynnwys priflythrennau", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> bach", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "<PERSON><PERSON><PERSON>s rhi<PERSON>u", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> nodau arbennig", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON> o eiriau"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "<PERSON><PERSON><PERSON>s rhif"}, "minNumbers": {"message": "Isafswm rhifau"}, "minSpecial": {"message": "Isafswm nodau arbennig"}, "avoidAmbiguous": {"message": "Osgoi nodau amwys", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> gell"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "Does dim eitemau i'w rhestru."}, "itemInformation": {"message": "Gwybodaeth am yr eitem"}, "username": {"message": "Enw defnyddiwr"}, "password": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "Cyfrinymadrodd"}, "favorite": {"message": "<PERSON>fe<PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "Nodiadau"}, "privateNote": {"message": "Nodyn preifat"}, "note": {"message": "Nodyn"}, "editItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>m"}, "folder": {"message": "<PERSON><PERSON><PERSON>"}, "deleteItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>m"}, "viewItem": {"message": "<PERSON><PERSON><PERSON> yr eitem"}, "launch": {"message": "Lansio"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "<PERSON><PERSON><PERSON>"}, "toggleVisibility": {"message": "Tog<PERSON> gweled<PERSON>"}, "manage": {"message": "<PERSON><PERSON><PERSON>"}, "other": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> eraill"}, "unlockMethods": {"message": "Dewisiadau datgloi"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Set up an unlock method to change your vault timeout action."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> eraill"}, "rateExtension": {"message": "<PERSON><PERSON><PERSON> eich barn ar yr estyniad"}, "browserNotSupportClipboard": {"message": "Dyw eich porwr gwe ddim yn cefnogi copïo drwy'r clipfwrdd yn hawdd. Copïwch â llaw yn lle."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "Mae eich cell ar glo. Gwiriwch eich hunaniaeth i barhau."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "Datgloi"}, "loggedInAsOn": {"message": "Wedi mewngofnodi gyda $EMAIL$ ar $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> annilys"}, "vaultTimeout": {"message": "C<PERSON><PERSON>'r gell"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "C<PERSON>i nawr"}, "lockAll": {"message": "C<PERSON><PERSON>'r cwbl"}, "immediately": {"message": "ar unwaith"}, "tenSeconds": {"message": "ar <PERSON>l 10 eiliad"}, "twentySeconds": {"message": "ar ôl 20 eiliad"}, "thirtySeconds": {"message": "ar <PERSON>l 30 eiliad"}, "oneMinute": {"message": "ar <PERSON><PERSON> munud"}, "twoMinutes": {"message": "ar ôl 2 funud"}, "fiveMinutes": {"message": "ar <PERSON>l 5 munud"}, "fifteenMinutes": {"message": "ar <PERSON>l chwarter awr"}, "thirtyMinutes": {"message": "ar <PERSON>l hanner awr"}, "oneHour": {"message": "ar ôl awr"}, "fourHours": {"message": "ar <PERSON>l 4 awr"}, "onLocked": {"message": "On system lock"}, "onRestart": {"message": "wrth ailgychwyn y porwr"}, "never": {"message": "Byth"}, "security": {"message": "Diogelw<PERSON>"}, "confirmMasterPassword": {"message": "Cadarn<PERSON><PERSON>'r prif gy<PERSON>ir"}, "masterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "<PERSON>u gwall"}, "emailRequired": {"message": "<PERSON> angen cyfeiriad e<PERSON>."}, "invalidEmail": {"message": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>t annilys."}, "masterPasswordRequired": {"message": "Mae angen prif gyfrinair."}, "confirmMasterPasswordRequired": {"message": "<PERSON> angen aildeipio'r prif gyfrinair."}, "masterPasswordMinlength": {"message": "<PERSON><PERSON><PERSON> i'r prif gyfrinair gynnwys o leiaf $VALUE$ nod.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Master password confirmation does not match."}, "newAccountCreated": {"message": "<PERSON> eich cyfrif newydd wedi cael ei greu! Gallwch bellach fewngo<PERSON>nodi."}, "newAccountCreated2": {"message": "<PERSON> eich cyfrif wedi cael ei greu!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "Rydym ni wedi anfon ebost atoch gydag awgrym ar gyfer eich prif gyfrinair."}, "verificationCodeRequired": {"message": "Mae angen cod dilysu."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "Cod dilysu annilys"}, "valueCopied": {"message": "$VALUE$ wedi'i gopïo", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Unable to autofill the selected item on this page. Copy and paste the information instead."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "Logged out"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "<PERSON> eich sesiwn wedi dod i ben."}, "logIn": {"message": "Mewngofnodi"}, "logInToBitwarden": {"message": "Mewngofnodi i Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "Mae'n bosib fod gennych gyfrif eisoes"}, "logOutConfirmation": {"message": "Y<PERSON>ch chi'n siŵr eich bod am allgofnodi?"}, "yes": {"message": "Ydw"}, "no": {"message": "Na"}, "location": {"message": "Lleoliad"}, "unexpectedError": {"message": "Digwyddodd gwall annisgwyl."}, "nameRequired": {"message": "<PERSON> angen enw."}, "addedFolder": {"message": "<PERSON><PERSON><PERSON> wed<PERSON><PERSON><PERSON> h<PERSON><PERSON><PERSON>u"}, "twoStepLoginConfirmation": {"message": "Two-step login makes your account more secure by requiring you to verify your login with another device such as a security key, authenticator app, SMS, phone call, or email. Two-step login can be set up on the bitwarden.com web vault. Do you want to visit the website now?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "<PERSON><PERSON>er wedi'i chadw"}, "deleteFolderConfirmation": {"message": "Are you sure you want to delete this folder?"}, "deletedFolder": {"message": "<PERSON><PERSON><PERSON> wedi'i dileu"}, "gettingStartedTutorial": {"message": "Getting started tutorial"}, "gettingStartedTutorialVideo": {"message": "Watch our getting started tutorial to learn how to get the most out of the browser extension."}, "syncingComplete": {"message": "<PERSON><PERSON><PERSON> wedi'i gwblhau"}, "syncingFailed": {"message": "Methwyd â chysoni"}, "passwordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON> wed<PERSON><PERSON><PERSON> go<PERSON>"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "URI newydd"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "<PERSON>ite<PERSON> wedi'i h<PERSON>u"}, "editedItem": {"message": "Eitem wedi'i chadw"}, "deleteItemConfirmation": {"message": "Ydych chi wir eisiau anfon i'r sbwriel?"}, "deletedItem": {"message": "Anfonwyd yr eitem i'r sbwriel"}, "overwritePassword": {"message": "Trosysgrifo'r cyfrinair"}, "overwritePasswordConfirmation": {"message": "Are you sure you want to overwrite the current password?"}, "overwriteUsername": {"message": "Trosysgrifo'r enw defnyddiwr"}, "overwriteUsernameConfirmation": {"message": "Are you sure you want to overwrite the current username?"}, "searchFolder": {"message": "<PERSON><PERSON><PERSON> d<PERSON>r f<PERSON>er"}, "searchCollection": {"message": "<PERSON><PERSON><PERSON> d<PERSON>r casgliad"}, "searchType": {"message": "<PERSON><PERSON><PERSON> drwy'r math hwn"}, "noneFolder": {"message": "<PERSON><PERSON> ffolder", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Gofyn i ychwanegu manylion mewngofnodi"}, "vaultSaveOptionsTitle": {"message": "Dewisiadau cadw i'r gell"}, "addLoginNotificationDesc": {"message": "Ask to add an item if one isn't found in your vault."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "Show cards on Tab page"}, "showCardsCurrentTabDesc": {"message": "List card items on the Tab page for easy autofill."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "Show identities on Tab page"}, "showIdentitiesCurrentTabDesc": {"message": "List identity items on the Tab page for easy autofill."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON>'r clipfwrdd", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Automatically clear copied values from your clipboard.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "A d<PERSON><PERSON> gofio'r cy<PERSON><PERSON>ir hwn i chi?"}, "notificationAddSave": {"message": "Cadw"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Cadw fel manylion mewngofnodi newydd", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "<PERSON><PERSON><PERSON> i ddiweddaru manylion mewngofnodi sy'n bodoli eisoes"}, "changedPasswordNotificationDesc": {"message": "Ask to update a login's password when a change is detected on a website."}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "Ask to save and use passkeys"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "Do you want to update this password in Bitwarden?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "Unlock your Bitwarden vault to complete the autofill request."}, "notificationUnlock": {"message": "Datgloi"}, "additionalOptions": {"message": "Dewisiadau ychwanegol"}, "enableContextMenuItem": {"message": "Show context menu options"}, "contextMenuItemDesc": {"message": "Use a secondary click to access password generation and matching logins for the website."}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "Default URI match detection", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Choose the default way that URI match detection is handled for logins when performing actions such as autofill."}, "theme": {"message": "<PERSON>a"}, "themeDesc": {"message": "Change the application's color theme."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "Tywyll", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Allforio'r gell"}, "fileFormat": {"message": "Fformat y ffeil"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "RHYBUDD", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Rhybudd", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Confirm vault export"}, "exportWarningDesc": {"message": "This export contains your vault data in an unencrypted format. You should not store or send the exported file over unsecure channels (such as email). Delete it immediately after you are done using it."}, "encExportKeyWarningDesc": {"message": "This export encrypts your data using your account's encryption key. If you ever rotate your account's encryption key you should export again since you will not be able to decrypt this export file."}, "encExportAccountWarningDesc": {"message": "Account encryption keys are unique to each Bitwarden user account, so you can't import an encrypted export into a different account."}, "exportMasterPassword": {"message": "Enter your master password to export your vault data."}, "shared": {"message": "Shared"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "Move to organization"}, "movedItemToOrg": {"message": "Symudwyd $ITEMNAME$ i $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Choose an organization that you wish to move this item to. Moving to an organization transfers ownership of the item to that organization. You will no longer be the direct owner of this item once it has been moved."}, "learnMore": {"message": "Dysgu mwy"}, "authenticatorKeyTotp": {"message": "Authenticator key (TOTP)"}, "verificationCodeTotp": {"message": "Verification code (TOTP)"}, "copyVerificationCode": {"message": "Copy verification code"}, "attachments": {"message": "Atodiadau"}, "deleteAttachment": {"message": "Delete attachment"}, "deleteAttachmentConfirmation": {"message": "Are you sure you want to delete this attachment?"}, "deletedAttachment": {"message": "Attachment deleted"}, "newAttachment": {"message": "Ychwanegu atodiad newydd"}, "noAttachments": {"message": "<PERSON>m atodia<PERSON>."}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON><PERSON> saved"}, "file": {"message": "Ffeil"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "<PERSON><PERSON><PERSON> f<PERSON>"}, "maxFileSize": {"message": "Maximum file size is 500 MB."}, "featureUnavailable": {"message": "Feature unavailable"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Aelodaeth uwch"}, "premiumManage": {"message": "Rheoli'ch aelodaeth"}, "premiumManageAlert": {"message": "You can manage your membership on the bitwarden.com web vault. Do you want to visit the website now?"}, "premiumRefresh": {"message": "Adnewydd<PERSON><PERSON><PERSON> a<PERSON>"}, "premiumNotCurrentMember": {"message": "Does gennych chi ddim aeloaeth uwch ar hyn o bryd."}, "premiumSignUpAndGet": {"message": "Cofrestrwch ar gyfer aelodaeth uwch i gael:"}, "ppremiumSignUpStorage": {"message": "Storfa 1GB wedi'i hamgryptio ar gyfer atodiadau ffeiliau."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Dewisiadau mewngofnodi dau gam perchenogol megis <PERSON> a Duo."}, "ppremiumSignUpReports": {"message": "Password hygiene, account health, and data breach reports to keep your vault safe."}, "ppremiumSignUpTotp": {"message": "Cynhyrchydd codau dilysu TOTP (2FA) ar gyfer manylion mewngofnodi yn eich cell."}, "ppremiumSignUpSupport": {"message": "Cymorth wedi'i flaenoriaethu."}, "ppremiumSignUpFuture": {"message": "All future Premium features. More coming soon!"}, "premiumPurchase": {"message": "<PERSON><PERSON><PERSON> uwch"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Mae gennych aelodaeth uwch!"}, "premiumCurrentMemberThanks": {"message": "Diolch am gefnogi Bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "Hyn oll am $PRICE$ y flwyddyn!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Refresh complete"}, "enableAutoTotpCopy": {"message": "Copy TOTP automatically"}, "disableAutoTotpCopyDesc": {"message": "If a login has an authenticator key, copy the TOTP verification code to your clip-board when you autofill the login."}, "enableAutoBiometricsPrompt": {"message": "Ask for biometrics on launch"}, "premiumRequired": {"message": "<PERSON> angen aelodaeth uwch"}, "premiumRequiredDesc": {"message": "<PERSON> angen aelodaeth uwch i ddefnyddio'r nodwedd hon."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Anfonwyd ebost dilysu i $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Insert your security key into your computer's USB port. If it has a button, touch it."}, "openInNewTab": {"message": "Agor mewn tab newydd"}, "webAuthnAuthenticate": {"message": "Authenticate WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "Login unavailable"}, "noTwoStepProviders": {"message": "This account has two-step login set up, however, none of the configured two-step providers are supported by this web browser."}, "noTwoStepProviders2": {"message": "Please use a supported web browser (such as Chrome) and/or add additional providers that are better supported across web browsers (such as an authenticator app)."}, "twoStepOptions": {"message": "Dewisiadau mewngofnodi dau gam"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Lost access to all of your two-factor providers? Use your recovery code to turn off all two-factor providers from your account."}, "recoveryCodeTitle": {"message": "Cod adfer"}, "authenticatorAppTitle": {"message": "Ap dilysu"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Use a YubiKey to access your account. Works with YubiKey 4, 4 Nano, 4C, and NEO devices."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verify with Duo Security for your organization using the Duo Mobile app, SMS, phone call, or U2F security key.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Use any WebAuthn compatible security key to access your account."}, "emailTitle": {"message": "Ebost"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Self-hosted environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Amgylchedd addasedig"}, "baseUrl": {"message": "Server URL"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API server URL"}, "webVaultUrl": {"message": "Web vault server URL"}, "identityUrl": {"message": "Identity server URL"}, "notificationsUrl": {"message": "Notifications server URL"}, "iconsUrl": {"message": "Icons server URL"}, "environmentSaved": {"message": "Environment URLs saved"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Diffoddwch osodiadau rheolydd cyfrineiriau eich porwr er mwyn osgoi gwrthdaro."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "Off", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoadDesc": {"message": "Llenwi'n awtomatig wrth i dudalen lwytho os canfyddir ffurflen mewngofnodi."}, "experimentalFeature": {"message": "Compromised or untrusted websites can exploit autofill on page load."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Dysgu mwy am lenwi'n awtomatig"}, "defaultAutoFillOnPageLoad": {"message": "Default autofill setting for login items"}, "defaultAutoFillOnPageLoadDesc": {"message": "You can turn off autofill on page load for individual login items from the item's Edit view."}, "itemAutoFillOnPageLoad": {"message": "Autofill on page load (if set up in Options)"}, "autoFillOnPageLoadUseDefault": {"message": "Defnyddio'r go<PERSON><PERSON><PERSON> rhagosodedig"}, "autoFillOnPageLoadYes": {"message": "Autofill on page load"}, "autoFillOnPageLoadNo": {"message": "Do not autofill on page load"}, "commandOpenPopup": {"message": "Open vault popup"}, "commandOpenSidebar": {"message": "Open vault in sidebar"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Cynhyrchu a chopïo cyfrinair hap newydd i'r clipfwrdd"}, "commandLockVaultDesc": {"message": "C<PERSON><PERSON>'r gell"}, "customFields": {"message": "Meysydd addasedig"}, "copyValue": {"message": "Copy value"}, "value": {"message": "Gwerth"}, "newCustomField": {"message": "<PERSON><PERSON> addasedig <PERSON>d"}, "dragToSort": {"message": "Drag to sort"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "<PERSON><PERSON>"}, "cfTypeHidden": {"message": "<PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Gwerth Boole"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "Linked", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Linked value", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Clicking outside the popup window to check your email for your verification code will cause this popup to close. Do you want to open this popup in a new window so that it does not close?"}, "popupU2fCloseMessage": {"message": "This browser cannot process U2F requests in this popup window. Do you want to open this popup in a new window so that you can log in using U2F?"}, "enableFavicon": {"message": "Dangos eiconau gwefannau"}, "faviconDesc": {"message": "Dangos delwedd adnabyddadwy wrth ymyl pob eitem."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "Show badge counter"}, "badgeCounterDesc": {"message": "Indicate how many logins you have for the current web page."}, "cardholderName": {"message": "Enw ar y cerdyn"}, "number": {"message": "<PERSON><PERSON><PERSON>"}, "brand": {"message": "Brand"}, "expirationMonth": {"message": "Mis dod i ben"}, "expirationYear": {"message": "<PERSON><PERSON><PERSON>ddyn dod i ben"}, "expiration": {"message": "<PERSON>d i ben"}, "january": {"message": "Ionawr"}, "february": {"message": "Chwef<PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON><PERSON>"}, "april": {"message": "Ebrill"}, "may": {"message": "<PERSON>"}, "june": {"message": "<PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "august": {"message": "Awst"}, "september": {"message": "Medi"}, "october": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "november": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "december": {"message": "Rhagfyr"}, "securityCode": {"message": "Cod diogelwch"}, "ex": {"message": "engh."}, "title": {"message": "<PERSON><PERSON><PERSON>"}, "mr": {"message": "Mr"}, "mrs": {"message": "Mrs"}, "ms": {"message": "Ms"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON> c<PERSON>f"}, "middleName": {"message": "Enw canol"}, "lastName": {"message": "Cyfenw"}, "fullName": {"message": "<PERSON>w llawn"}, "identityName": {"message": "Identity name"}, "company": {"message": "Cwmni"}, "ssn": {"message": "Social Security number"}, "passportNumber": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "licenseNumber": {"message": "<PERSON><PERSON><PERSON> trwy<PERSON>"}, "email": {"message": "Ebost"}, "phone": {"message": "Ffôn"}, "address": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "address1": {"message": "Cyfeiriad 1"}, "address2": {"message": "Cyfeiriad 2"}, "address3": {"message": "Cyfeiriad 3"}, "cityTown": {"message": "Tref / Dinas"}, "stateProvince": {"message": "Talaith / Rhanbarth"}, "zipPostalCode": {"message": "Cod post / zip"}, "country": {"message": "<PERSON><PERSON><PERSON>"}, "type": {"message": "Math"}, "typeLogin": {"message": "<PERSON><PERSON> mewngofnodi"}, "typeLogins": {"message": "<PERSON><PERSON> mewngofnodi"}, "typeSecureNote": {"message": "<PERSON><PERSON><PERSON>"}, "typeCard": {"message": "<PERSON><PERSON><PERSON>"}, "typeIdentity": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeSshKey": {"message": "Allwedd SSH"}, "newItemHeader": {"message": "$TYPE$ newydd", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Golygu $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Gweld $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "<PERSON><PERSON>"}, "generatorHistory": {"message": "Hanes y cynhyrchydd"}, "clearGeneratorHistoryTitle": {"message": "<PERSON><PERSON><PERSON> hanes y cynhyrchydd"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "<PERSON><PERSON> <PERSON><PERSON>"}, "collections": {"message": "Casgliadau"}, "nCollections": {"message": "$COUNT$ o gasgliadau", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Ffefrynnau"}, "popOutNewWindow": {"message": "Syumd i ffenestr newydd"}, "refresh": {"message": "Adnewyddu"}, "cards": {"message": "<PERSON><PERSON><PERSON>"}, "identities": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "logins": {"message": "<PERSON><PERSON> mewngofnodi"}, "secureNotes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sshKeys": {"message": "Allweddi SSH"}, "clear": {"message": "<PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON> a ydy'r cyfrinair wedi'i ddat<PERSON>u."}, "passwordExposed": {"message": "Mae'r cyf<PERSON>ir hwn wedi cael ei ddatgelu $VALUE$ o weithiau mewn achosion o dorri data. Dylech chi ei newid.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Chafodd y cyfrinair hwn mo'i ganfod mewn unrhyw achos hysbys o dorri data. Dylai fod yn iawn i'w ddefnyddio."}, "baseDomain": {"message": "Base domain", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "<PERSON><PERSON> sylfaen (awgrymir)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "Enw parth", "description": "Domain name. Ex. website.com"}, "host": {"message": "Host", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Union gywir"}, "startsWith": {"message": "Yn dechrau â"}, "regEx": {"message": "Mynegiant rheolaidd", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Default match detection", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Toggle options"}, "toggleCurrentUris": {"message": "Toggle current URIs", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "URI cyfredol", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Sefydliad", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON>"}, "allItems": {"message": "Pob eitem"}, "noPasswordsInList": {"message": "Does dim cyfrineiriau i'w rhestru."}, "clearHistory": {"message": "<PERSON><PERSON><PERSON>'r hanes"}, "nothingToShow": {"message": "Dim byd i'w ddangos"}, "nothingGeneratedRecently": {"message": "<PERSON><PERSON><PERSON> chi heb gynhyrchu unrhyw beth yn ddiweddar"}, "remove": {"message": "<PERSON><PERSON><PERSON>"}, "default": {"message": "<PERSON><PERSON><PERSON>"}, "dateUpdated": {"message": "Updated", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Created", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON> wed<PERSON><PERSON>i d<PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Are you sure you want to use the \"Never\" option? Setting your lock options to \"Never\" stores your vault's encryption key on your device. If you use this option you should ensure that you keep your device properly protected."}, "noOrganizationsList": {"message": "You do not belong to any organizations. Organizations allow you to securely share items with other users."}, "noCollectionsInList": {"message": "Does dim casgliadau i'w rhestru."}, "ownership": {"message": "Perchnogaeth"}, "whoOwnsThisItem": {"message": "Pwy sy'n berchen ar yr eitem hon?"}, "strong": {"message": "<PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "Da", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> gwan"}, "weakMasterPasswordDesc": {"message": "Mae'r prif gyfrinair rydych chi wedi'i ddewis yn wan. <PERSON><PERSON><PERSON> ddefnyddio prif gyfrinair (neu gyfrinymadrodd) cryf i amddiffyn eich cyfrif Bitwarden. Ydych chi'n siŵr eich bod am ddefnyddio'r prif gyfrinair hwn?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Datgloi â PIN"}, "setYourPinTitle": {"message": "Gosod PIN"}, "setYourPinButton": {"message": "Gosod PIN"}, "setYourPinCode": {"message": "Set your PIN code for unlocking <PERSON>warden. Your PIN settings will be reset if you ever fully log out of the application."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "Mae angen cod PIN."}, "invalidPin": {"message": "Cod PIN annilys."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "Datgloi â biometreg"}, "unlockWithMasterPassword": {"message": "Datgloi gyda'r prif gy<PERSON>ir"}, "awaitDesktop": {"message": "Awaiting confirmation from desktop"}, "awaitDesktopDesc": {"message": "Please confirm using biometrics in the Bitwarden desktop application to set up biometrics for browser."}, "lockWithMasterPassOnRestart": {"message": "Lock with master password on browser restart"}, "lockWithMasterPassOnRestart1": {"message": "Gofyn am y prif gyfrinair wrth ailgychwyn y porwr"}, "selectOneCollection": {"message": "You must select at least one collection."}, "cloneItem": {"message": "Clone item"}, "clone": {"message": "<PERSON><PERSON>"}, "passwordGenerator": {"message": "Cynh<PERSON><PERSON><PERSON>"}, "usernameGenerator": {"message": "Cynhyrychydd enwau defnyddiwr"}, "useThisEmail": {"message": "Defnyddio'r e<PERSON><PERSON> hwn"}, "useThisPassword": {"message": "Defnyddio'r cyfrinair hwn"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Def<PERSON>dd<PERSON>'r enw defnyddiwr hwn"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Defnyddiwch y cynhyrchydd", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "i greu cyfrinair cryf ac unigryw", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "Vault timeout action"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "lock": {"message": "Cloi", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Sbwriel", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "<PERSON><PERSON><PERSON> dr<PERSON>r sbw<PERSON>"}, "permanentlyDeleteItem": {"message": "<PERSON><PERSON><PERSON><PERSON>r eitem yn barhaol"}, "permanentlyDeleteItemConfirmation": {"message": "Are you sure you want to permanently delete this item?"}, "permanentlyDeletedItem": {"message": "Eitem wedi'i dileu'n barhaol"}, "restoreItem": {"message": "Adfer yr eitem"}, "restoredItem": {"message": "Item restored"}, "alreadyHaveAccount": {"message": "Oes gennych chi gyfrif eisoes?"}, "vaultTimeoutLogOutConfirmation": {"message": "Logging out will remove all access to your vault and requires online authentication after the timeout period. Are you sure you want to use this setting?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Timeout action confirmation"}, "autoFillAndSave": {"message": "<PERSON><PERSON><PERSON>'n awtomatig a chadw"}, "fillAndSave": {"message": "<PERSON><PERSON><PERSON> a chadw"}, "autoFillSuccessAndSavedUri": {"message": "Item autofilled and URI saved"}, "autoFillSuccess": {"message": "Item autofilled "}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON><PERSON>d prif gyf<PERSON>ir"}, "currentMasterPass": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> presennol"}, "newMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confirmNewMasterPass": {"message": "Cadarnhau'r prif g<PERSON><PERSON><PERSON>d"}, "masterPasswordPolicyInEffect": {"message": "One or more organization policies require your master password to meet the following requirements:"}, "policyInEffectMinComplexity": {"message": "Minimum complexity score of $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimum length of $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Contain one or more uppercase characters"}, "policyInEffectLowercase": {"message": "Contain one or more lowercase characters"}, "policyInEffectNumbers": {"message": "Contain one or more numbers"}, "policyInEffectSpecial": {"message": "Contain one or more of the following special characters $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Your new master password does not meet the policy requirements."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "<PERSON><PERSON> dicio'r blwch hwn, rydych yn cytuno i'r canlynol:"}, "acceptPoliciesRequired": {"message": "Terms of Service and Privacy Policy have not been acknowledged."}, "termsOfService": {"message": "Telerau g<PERSON>"}, "privacyPolicy": {"message": "Polisi preifatrwydd"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Your password hint cannot be the same as your password."}, "ok": {"message": "<PERSON><PERSON><PERSON>"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Desktop sync verification"}, "desktopIntegrationVerificationText": {"message": "Gwiriwch fod y rhaglen bwrdd gwaith yn dangos yr ymadrodd hwn: "}, "desktopIntegrationDisabledTitle": {"message": "Browser integration is not set up"}, "desktopIntegrationDisabledDesc": {"message": "Browser integration is not set up in the Bitwarden desktop application. Please set it up in the settings within the desktop application."}, "startDesktopTitle": {"message": "Start the Bitwarden desktop application"}, "startDesktopDesc": {"message": "The Bitwarden desktop application needs to be started before unlock with biometrics can be used."}, "errorEnableBiometricTitle": {"message": "Unable to set up biometrics"}, "errorEnableBiometricDesc": {"message": "Action was canceled by the desktop application"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Desktop application invalidated the secure communication channel. Please retry this operation"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Desktop communication interrupted"}, "nativeMessagingWrongUserDesc": {"message": "The desktop application is logged into a different account. Please ensure both applications are logged into the same account."}, "nativeMessagingWrongUserTitle": {"message": "Account missmatch"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Biometrics not set up"}, "biometricsNotEnabledDesc": {"message": "Browser biometrics requires desktop biometric to be set up in the settings first."}, "biometricsNotSupportedTitle": {"message": "Biometrics not supported"}, "biometricsNotSupportedDesc": {"message": "Browser biometrics is not supported on this device."}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "Permission not provided"}, "nativeMessaginPermissionErrorDesc": {"message": "Without permission to communicate with the Bitwarden Desktop Application we cannot provide biometrics in the browser extension. Please try again."}, "nativeMessaginPermissionSidebarTitle": {"message": "Permission request error"}, "nativeMessaginPermissionSidebarDesc": {"message": "This action cannot be done in the sidebar, please retry the action in the popup or popout."}, "personalOwnershipSubmitError": {"message": "Due to an Enterprise Policy, you are restricted from saving items to your personal vault. Change the Ownership option to an organization and choose from available collections."}, "personalOwnershipPolicyInEffect": {"message": "An organization policy is affecting your ownership options."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "<PERSON><PERSON><PERSON> wedi'u heith<PERSON>"}, "excludedDomainsDesc": {"message": "Fydd Bitwarden ddim yn gofyn i gadw manylion mewngofnodi'r parthau hyn. Rhaid i chi ail-lwytho'r dudalen i newidiadau ddod i rym."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mewn perygl"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "Dyw $DOMAIN$ ddim yn barth dilys", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "<PERSON><PERSON>"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "Ffeil"}, "allSends": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "<PERSON>di dod i ben"}, "passwordProtected": {"message": "Password protected"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Copy Send link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Remove Password"}, "delete": {"message": "<PERSON><PERSON><PERSON>"}, "removedPassword": {"message": "Password removed"}, "deletedSend": {"message": "Send deleted", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Disabled"}, "removePasswordConfirmation": {"message": "Ydych chi'n sicr yr hoffech chi dynnu'r cyfrinair?"}, "deleteSend": {"message": "Delete Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Are you sure you want to delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Edit Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON><PERSON>u"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON><PERSON><PERSON> dod i ben"}, "oneDay": {"message": "1 diwrnod"}, "days": {"message": "$DAYS$ o ddyddiau", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Addasedig"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "New Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendDisabled": {"message": "Send removed", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Due to an enterprise policy, you are only able to delete an existing Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send created", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send saved", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "In order to choose a file, open the extension in the sidebar (if possible) or pop out to a new window by clicking this banner."}, "sendFirefoxFileWarning": {"message": "In order to choose a file using Firefox, open the extension in the sidebar or pop out to a new window by clicking this banner."}, "sendSafariFileWarning": {"message": "In order to choose a file using Safari, pop out to a new window by clicking this banner."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "Cyn i chi ddechrau"}, "expirationDateIsInvalid": {"message": "Dyw'r dyddiad dod i ben a roddwyd ddim yn ddilys."}, "deletionDateIsInvalid": {"message": "Dyw'r dyddiad dileu a roddwyd ddim yn ddilys."}, "expirationDateAndTimeRequired": {"message": "Mae angen rhoi dyddiad ac amser dod i ben."}, "deletionDateAndTimeRequired": {"message": "Mae angen rhoi dyddiad ac amser dileu."}, "dateParsingError": {"message": "There was an error saving your deletion and expiration dates."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "Ailofyn am y prif gyfrinair"}, "passwordConfirmation": {"message": "Master password confirmation"}, "passwordConfirmationDesc": {"message": "This action is protected. To continue, please re-enter your master password to verify your identity."}, "emailVerificationRequired": {"message": "Email verification required"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "You must verify your email to use this feature. You can verify your email in the web vault."}, "updatedMasterPassword": {"message": "Diweddar<PERSON>d y prif gyfrinair"}, "updateMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prif g<PERSON><PERSON>"}, "updateMasterPasswordWarning": {"message": "Your master password was recently changed by an administrator in your organization. In order to access the vault, you must update it now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatic enrollment"}, "resetPasswordAutoEnrollInviteWarning": {"message": "This organization has an enterprise policy that will automatically enroll you in password reset. Enrollment will allow organization administrators to change your master password."}, "selectFolder": {"message": "<PERSON><PERSON><PERSON> f<PERSON>er..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "Hours"}, "minutes": {"message": "Minutes"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Your organization policies have set your maximum allowed vault timeout to $HOURS$ hour(s) and $MINUTES$ minute(s).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Your vault timeout exceeds the restrictions set by your organization."}, "vaultExportDisabled": {"message": "Vault export unavailable"}, "personalVaultExportPolicyInEffect": {"message": "One or more organization policies prevents you from exporting your individual vault."}, "copyCustomFieldNameInvalidElement": {"message": "Unable to identify a valid form element. Try inspecting the HTML instead."}, "copyCustomFieldNameNotUnique": {"message": "No unique identifier found."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Leave organization"}, "removeMasterPassword": {"message": "Remove master password"}, "removedMasterPassword": {"message": "Master password removed"}, "leaveOrganizationConfirmation": {"message": "Are you sure you want to leave this organization?"}, "leftOrganization": {"message": "You have left the organization."}, "toggleCharacterCount": {"message": "Toggle character count"}, "sessionTimeout": {"message": "Your session has timed out. Please go back and try logging in again."}, "exportingPersonalVaultTitle": {"message": "Exporting individual vault"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Gwall"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Cynh<PERSON><PERSON> enw defnyddiwr"}, "generateEmail": {"message": "Cynh<PERSON><PERSON>"}, "spinboxBoundariesHint": {"message": "<PERSON>haid iddo fod rhwng $MIN$ a $MAX$ nod.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Defnyddiwch $RECOMMENDED$ neu fwy o nodau i gynhyrchu cyfrinair cryf.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Defnyddiwch $RECOMMENDED$ neu fwy o eiriau i gynhyrchu cyfrinymadrodd cryff.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus addressed email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Use your email provider's sub-addressing capabilities."}, "catchallEmail": {"message": "Catch-all email"}, "catchallEmailDesc": {"message": "Use your domain's configured catch-all inbox."}, "random": {"message": "<PERSON>p"}, "randomWord": {"message": "<PERSON><PERSON> ar hap"}, "websiteName": {"message": "Website name"}, "service": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "forwardedEmail": {"message": "Forwarded email alias"}, "forwardedEmailDesc": {"message": "Generate an email alias with an external forwarding service."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Cynhyrchwyd gan Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Tocyn API $SERVICENAME$ annilys", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Tocyn API $SERVICENAME$ annilys: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Hostname", "description": "Part of a URL."}, "apiAccessToken": {"message": "API Access Token"}, "apiKey": {"message": "Allwedd API"}, "ssoKeyConnectorError": {"message": "Key connector error: make sure key connector is available and working correctly."}, "premiumSubcriptionRequired": {"message": "Premium subscription required"}, "organizationIsDisabled": {"message": "Organization suspended."}, "disabledOrganizationFilterError": {"message": "Items in suspended Organizations cannot be accessed. Contact your Organization owner for assistance."}, "loggingInTo": {"message": "Yn mewngofnodi i $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Fersiwn y gweinydd"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Third-party"}, "thirdPartyServerMessage": {"message": "Connected to third-party server implementation, $SERVERNAME$. Please verify bugs using the official server, or report them to the third-party server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "gwelwyd ddiwethaf: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Mewngofnodi â'ch prif gyfrinair"}, "newAroundHere": {"message": "Ydych chi'n <PERSON><PERSON><PERSON>?"}, "rememberEmail": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "loginWithDevice": {"message": "Mewngofnodi â dyfais"}, "fingerprintPhraseHeader": {"message": "Ymadrodd unigryw"}, "fingerprintMatchInfo": {"message": "Sicrhewch fod eich cell wedi'i datgloi a bod yr ymadrodd unigryw yn cyfateb i'r un ar y ddyfais arall."}, "resendNotification": {"message": "Resend notification"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Cafodd y cyfrinair ei ganfod mewn achos o ddatgelu data. Defnyddiwch gyfrinair unigryw i ddiogelu eich cyfrif. Ydych chi wir eisiau defnyddio cyfrinair sydd wedi'i ddatgelu?"}, "weakAndExposedMasterPassword": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> gwan ac wedi'i ddat<PERSON>u"}, "weakAndBreachedMasterPasswordDesc": {"message": "Cyfrinair gwan a gafodd ei ganfod mewn achos o ddatgelu data. Defnyddiwch gyfrinair cryf ac unigryw i ddiogelu eich cyfrif. Ydych chi wir eisiau defnyddio cyfrinair sydd wedi'i ddatgelu?"}, "checkForBreaches": {"message": "Ch<PERSON>lio am achosion o ddatgelu data sy'n cynnwys y cyfrinair hwn"}, "important": {"message": "Pwysig:"}, "masterPasswordHint": {"message": "Allwch chi ddim adfer eich prif gyfrinair os caiff ei anghofio!"}, "characterMinimum": {"message": "Isafswm o $LENGTH$ nod", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Your organization policies have turned on autofill on page load."}, "howToAutofill": {"message": "Sut i lenwi'n awtomatig"}, "autofillSelectInfoWithCommand": {"message": "Dewiswch eitem o'r sgrin hon, defnyddiwch y llwybr byr $COMMAND$, neu darganfyddwch ddewisiadau eraill yn y gosodiadau.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "<PERSON><PERSON><PERSON>"}, "autofillSettings": {"message": "Gosodiadau llenwi awtom<PERSON>g"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Rheoli llwybrau byr"}, "autofillShortcut": {"message": "Autofill keyboard shortcut"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default autofill shortcut: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Opens in a new window"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Cofio'r d<PERSON><PERSON><PERSON> hon"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Dilynwch y ddolen yn yr ebost anfonwyd i"}, "andContinueCreatingYourAccount": {"message": "a pharhau i greu eich cyfrif."}, "noEmail": {"message": "Dim e<PERSON>?"}, "goBack": {"message": "Ewch yn ôl"}, "toEditYourEmailAddress": {"message": "i olygu eich cyfeiri<PERSON> e<PERSON>."}, "eu": {"message": "UE", "description": "European Union"}, "accessDenied": {"message": "Mynediad wedi ei wrthod. Does gennych chi ddim caniatâd i weld y dudalen hon."}, "general": {"message": "Cyffredinol"}, "display": {"message": "Display"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trafferth mewngofnodi?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "gofynnol"}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Dyw'r mewnbwn ddim yn gyfe<PERSON>ad e<PERSON>t."}, "fieldsNeedAttention": {"message": "Mae $COUNT$ o feysydd uchod angen sylw.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> --"}, "multiSelectPlaceholder": {"message": "-- <PERSON>ipi<PERSON><PERSON> i hidlo --"}, "multiSelectLoading": {"message": "Yn nô<PERSON> de<PERSON>..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON> gan<PERSON>d e<PERSON>"}, "multiSelectClearAll": {"message": "<PERSON><PERSON><PERSON><PERSON>r cyfan"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Alias domain"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Neidio i'r cynnwys"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Da<PERSON><PERSON><PERSON><PERSON> eich cyfrif i weld manylion mewngofnodi sy'n cyfateb", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Datg<PERSON>i eich cyfrif", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Dim eitemau i'w dangos", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "E<PERSON>m <PERSON>d", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Ychwanegu eitem newydd i'r gell", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "Manylion mewngofnodi newydd", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "<PERSON><PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Anwybydd<PERSON>"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Disgrifiad"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "Cafodd cyfanswm o $AMOUNT$ eitem eu mewnforio.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Ceisio eto"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Gosod PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Defnyddio PIN"}, "useBiometrics": {"message": "Defnyddio biometreg"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Ailanfon cod"}, "total": {"message": "Cyfanswm"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Cha<PERSON>dd dim ei fewnforio."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Dysgu am eich dewisiadau mewnforio"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Cadarnhau"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "<PERSON>w defnyddiwr neu gyfrinair anghywir"}, "incorrectPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "incorrectCode": {"message": "Cod anghywir"}, "incorrectPin": {"message": "PIN an<PERSON>wir"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "Ebost LastPass"}, "importingYourAccount": {"message": "Yn mewnforio eich cyfrif..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Mewnforio'n uniongyrchol o LastPass"}, "importFromCSV": {"message": "Mewnforio o CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Casgliad"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "<PERSON><PERSON> c<PERSON>f"}, "switchAccounts": {"message": "<PERSON><PERSON> c<PERSON>f"}, "switchToAccount": {"message": "Newid i gyfrif"}, "activeAccount": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Cyfrifon ar gael"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "gweinydd"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Unwaith yn unig"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "Ychwanegwyd $DOMAIN$ i'r parthau wedi'u heithrio.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Fformatau cyffredin", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Hoffech chi wneud Bitwarden yn rheolydd cyfrineiriau rhagosodedig?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Awgry<PERSON><PERSON><PERSON>"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "<PERSON>ogel<PERSON><PERSON> eich cyfrif"}, "notifications": {"message": "Hysbysiadau"}, "appearance": {"message": "Golwg"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "Gweld eitemau yn $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Yn ôl i $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "Newydd"}, "removeItem": {"message": "Tynnu $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Ychwanegu cyfrif"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Ychwanegu"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Golygu $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Dileu $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "Ychwanegwyd $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Allwedd breifat"}, "sshPublicKey": {"message": "Allwedd gyhoeddus"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Math o allwedd"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "<PERSON><PERSON>"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}