{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwarden 비밀번호 관리자", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "집에서도, 직장에서도, 이동 중에도 Bitwarden은 비밀번호, 패스키, 민감 정보를 쉽게 보호합니다.", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "안전 보관함에 접근하려면 로그인하거나 새 계정을 만드세요."}, "inviteAccepted": {"message": "초대 승낙"}, "createAccount": {"message": "계정 만들기"}, "newToBitwarden": {"message": "Bitwarden을 처음 이용하시나요?"}, "logInWithPasskey": {"message": "패스키를 사용하여 로그인하기"}, "useSingleSignOn": {"message": "통합인증(SSO) 사용하기"}, "welcomeBack": {"message": "돌아온 것을 환영합니다."}, "setAStrongPassword": {"message": "비밀번호 설정"}, "finishCreatingYourAccountBySettingAPassword": {"message": "비밀번호를 설정하여 계정 생성을 완료합니다."}, "enterpriseSingleSignOn": {"message": "엔터프라이즈 통합 인증 (SSO)"}, "cancel": {"message": "취소"}, "close": {"message": "닫기"}, "submit": {"message": "보내기"}, "emailAddress": {"message": "이메일 주소"}, "masterPass": {"message": "마스터 비밀번호"}, "masterPassDesc": {"message": "마스터 비밀번호는 보관함을 열 때 필요한 비밀번호입니다. 절대 마스터 비밀번호를 잊어버리지 마세요. 잊어버리면 복구할 수 있는 방법이 없습니다."}, "masterPassHintDesc": {"message": "마스터 비밀번호 힌트는 마스터 비밀번호를 잊었을 때 도움이 될 수 있습니다."}, "masterPassHintText": {"message": "비밀번호를 잊어버린 경우 비밀번호 힌트가 이메일로 전송됩니다. 최대 길이: $CURRENT$/$MAXIMUM$", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "마스터 비밀번호 다시 입력"}, "masterPassHint": {"message": "마스터 비밀번호 힌트 (선택)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "\"조직\"에 가입하기"}, "joinOrganizationName": {"message": "$ORGANIZATIONNAME$에 참가하기", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "마지막으로, 마스터 비밀번호를 설정하여 조직에 참가하십시오"}, "tab": {"message": "탭"}, "vault": {"message": "보관함"}, "myVault": {"message": "내 보관함"}, "allVaults": {"message": "모든 보관함"}, "tools": {"message": "도구"}, "settings": {"message": "설정"}, "currentTab": {"message": "현재 탭"}, "copyPassword": {"message": "비밀번호 복사"}, "copyPassphrase": {"message": "암호 복사"}, "copyNote": {"message": "메모 복사"}, "copyUri": {"message": "URI 복사"}, "copyUsername": {"message": "사용자 이름 복사"}, "copyNumber": {"message": "번호 복사"}, "copySecurityCode": {"message": "보안 코드 복사"}, "copyName": {"message": "이름 복사"}, "copyCompany": {"message": "회사 복사"}, "copySSN": {"message": "주민등록번호 복사"}, "copyPassportNumber": {"message": "여권 번호 복사"}, "copyLicenseNumber": {"message": "운전면허 번호 복사"}, "copyPrivateKey": {"message": "개인 키 복사"}, "copyPublicKey": {"message": "공개 키 복사"}, "copyFingerprint": {"message": "핑거프린트 복사"}, "copyCustomField": {"message": "$FIELD$ 복사", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "웹사이트 복사"}, "copyNotes": {"message": "노트 복사"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "채우기", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "자동 완성"}, "autoFillLogin": {"message": "로그인 자동 완성"}, "autoFillCard": {"message": "카드 자동 완성"}, "autoFillIdentity": {"message": "신원 자동 완성"}, "fillVerificationCode": {"message": "인증 코드를 입력하세요"}, "fillVerificationCodeAria": {"message": "인증 코드를 입력하세요", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "비밀번호 생성 및 클립보드에 복사"}, "copyElementIdentifier": {"message": "사용자 지정 필드 이름 복사"}, "noMatchingLogins": {"message": "사용할 수 있는 로그인이 없습니다."}, "noCards": {"message": "카드 없음"}, "noIdentities": {"message": "신원 없음"}, "addLoginMenu": {"message": "로그인 추가"}, "addCardMenu": {"message": "카드 추가"}, "addIdentityMenu": {"message": "신원 추가"}, "unlockVaultMenu": {"message": "보관함 잠금 해제"}, "loginToVaultMenu": {"message": "보관함에 로그인하기"}, "autoFillInfo": {"message": "이 탭의 자동 완성에 사용할 수 있는 로그인이 없습니다."}, "addLogin": {"message": "로그인 추가"}, "addItem": {"message": "항목 추가"}, "accountEmail": {"message": "계정 이메일"}, "requestHint": {"message": "힌트 요청"}, "requestPasswordHint": {"message": "마스터 비밀번호 힌트 얻기"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "계정 이메일 주소를 입력하세요. 그 주소로 비밀번호 힌트가 전송될 것 입니다."}, "getMasterPasswordHint": {"message": "마스터 비밀번호 힌트 얻기"}, "continue": {"message": "계속"}, "sendVerificationCode": {"message": "이메일로 인증 코드 보내기"}, "sendCode": {"message": "코드 전송"}, "codeSent": {"message": "코드 전송됨"}, "verificationCode": {"message": "인증 코드"}, "confirmIdentity": {"message": "계속하려면 암호를 확인하세요."}, "changeMasterPassword": {"message": "마스터 비밀번호 변경"}, "continueToWebApp": {"message": "웹 앱에서 계속하시겠나요?"}, "continueToWebAppDesc": {"message": "웹 앱에서 Bitwarden 계정의 더 많은 기능을 탐색해보세요."}, "continueToHelpCenter": {"message": "도움말 센터로 이동"}, "continueToHelpCenterDesc": {"message": "Bitwarden의 자세한 사용법은 도움말 센터에서 확인하세요."}, "continueToBrowserExtensionStore": {"message": "브라우저 확장 스토어로 이동하시겠습니까?"}, "continueToBrowserExtensionStoreDesc": {"message": "다른 사람들이 Bitwarden이 적합한지 알 수 있도록 도와주세요. 당신의 브라우저 확장 스토어로 방문하여 별점을 남겨주세요."}, "changeMasterPasswordOnWebConfirmation": {"message": "Bitwarden 웹 앱에서 마스터 비밀번호를 변경할 수 있습니다."}, "fingerprintPhrase": {"message": "지문 구절", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "계정 지문 구절", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "2단계 인증"}, "logOut": {"message": "로그아웃"}, "aboutBitwarden": {"message": "Bitwarden 에 대하여"}, "about": {"message": "정보"}, "moreFromBitwarden": {"message": "Bitwarden에 대한 더 많은 정보"}, "continueToBitwardenDotCom": {"message": "bitwarden.com 으로 이동할까요?"}, "bitwardenForBusiness": {"message": "비지니스용 Bitwarden"}, "bitwardenAuthenticator": {"message": "Bitwarden 인증 도구"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden 인증 도구를 사용하면, 인증키를 저장하고, 2단계 인증을 위한 TOTP 코드를 생성할 수 있습니다. 자세한 내용은 bitwarden.com 사이트에서 확인해주세요."}, "bitwardenSecretsManager": {"message": "Bitwarden 보안 매니저"}, "continueToSecretsManagerPageDesc": {"message": "Bitwarden 보안 매니저를 이용하여, 개발자의 기밀을 안전하게 저장하고, 관리하고, 공유하세요. 자세한 내용은 bitwarden.com 사이트에서 확인해주요."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Passwordless.dev와 함께, 기존의 비밀번호 로그인 방식으로 부터 벗어나, 매끄럽고 안전한 로그인 경험을 만들어보세요. 자세한 내용은 bitwarden.com 사이트에서 확인해주요"}, "freeBitwardenFamilies": {"message": "무료 bitwarden 가족 플랜"}, "freeBitwardenFamiliesPageDesc": {"message": "무료 Bitwarden 가족 플랜을 이용하실 수 있습니다. 오늘 웹앱에서 이 혜택을 사용하세요."}, "version": {"message": "버전"}, "save": {"message": "저장"}, "move": {"message": "이동"}, "addFolder": {"message": "폴더 추가"}, "name": {"message": "이름"}, "editFolder": {"message": "폴더 편집"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "새 폴더"}, "folderName": {"message": "폴더 이름"}, "folderHintText": {"message": "상위 폴더 이름 뒤에 \"/\"를 추가하여 폴더를 계층적으로 구성합니다. 예: Social/Forums"}, "noFoldersAdded": {"message": "추가된 폴더가 없습니다."}, "createFoldersToOrganize": {"message": "폴더를 만들어 보관함의 항목들을 정리해보세요"}, "deleteFolderPermanently": {"message": "정말로 이 폴더를 영구적으로 삭제하시겠습니까?"}, "deleteFolder": {"message": "폴더 삭제"}, "folders": {"message": "폴더"}, "noFolders": {"message": "폴더가 없습니다."}, "helpFeedback": {"message": "도움말 및 의견"}, "helpCenter": {"message": "Bitwarden 도움말 센터"}, "communityForums": {"message": "Bitwarden 커뮤니티 포럼 탐색하기"}, "contactSupport": {"message": "Bitwarden 지원에 문의하기"}, "sync": {"message": "동기화"}, "syncVaultNow": {"message": "지금 보관함 동기화"}, "lastSync": {"message": "마지막 동기화:"}, "passGen": {"message": "비밀번호 생성기"}, "generator": {"message": "생성기", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "유일무이하고 강력한 비밀번호를 자동으로 생성합니다."}, "bitWebVaultApp": {"message": "Bitwarden 웹 앱"}, "importItems": {"message": "항목 가져오기"}, "select": {"message": "선택"}, "generatePassword": {"message": "비밀번호 생성"}, "generatePassphrase": {"message": "암호 생성"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "비밀번호 재생성"}, "options": {"message": "옵션"}, "length": {"message": "길이"}, "include": {"message": "포함", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "대문자 포함", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "소문자 포함", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "숫자 포함", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "특수 문자 포함", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "단어 수"}, "wordSeparator": {"message": "구분 기호"}, "capitalize": {"message": "첫 글자를 대문자로", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "숫자 포함"}, "minNumbers": {"message": "숫자 최소 개수"}, "minSpecial": {"message": "특수 문자 최소 개수"}, "avoidAmbiguous": {"message": "모호한 문자 사용 안 함", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "기업 정책에 따른 요구사항들이 당신의 생성기 옵션들에 적용되었습니다.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "보관함 검색"}, "edit": {"message": "편집"}, "view": {"message": "보기"}, "noItemsInList": {"message": "항목이 없습니다."}, "itemInformation": {"message": "항목 정보"}, "username": {"message": "사용자 이름"}, "password": {"message": "비밀번호"}, "totp": {"message": "인증기 비밀 키"}, "passphrase": {"message": "패스프레이즈"}, "favorite": {"message": "즐겨찾기"}, "unfavorite": {"message": "즐겨찾기 해제"}, "itemAddedToFavorites": {"message": "항목이 즐겨찾기에 추가되었습니다."}, "itemRemovedFromFavorites": {"message": "항목이 즐겨찾기에서 삭제되었습니다."}, "notes": {"message": "메모"}, "privateNote": {"message": "개인 메모"}, "note": {"message": "메모"}, "editItem": {"message": "항목 편집"}, "folder": {"message": "폴더"}, "deleteItem": {"message": "항목 삭제"}, "viewItem": {"message": "항목 보기"}, "launch": {"message": "열기"}, "launchWebsite": {"message": "웹사이트 열기"}, "launchWebsiteName": {"message": "$ITEMNAME$ 웹사이드 열기", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "웹 사이트"}, "toggleVisibility": {"message": "표시 전환"}, "manage": {"message": "관리"}, "other": {"message": "기타"}, "unlockMethods": {"message": "잠금 해제 옵션"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "잠금 해제 방법을 설정하여 보관함의 시간 초과 동작을 변경하세요."}, "unlockMethodNeeded": {"message": "설정에서 잠금 해제 수단 설정하기"}, "sessionTimeoutHeader": {"message": "세션 만료"}, "vaultTimeoutHeader": {"message": "보관함 시간초과"}, "otherOptions": {"message": "기타 옵션"}, "rateExtension": {"message": "확장 프로그램 평가"}, "browserNotSupportClipboard": {"message": "사용하고 있는 웹 브라우저가 쉬운 클립보드 복사를 지원하지 않습니다. 직접 복사하세요."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "보관함이 잠겨 있습니다. 마스터 비밀번호를 입력하여 계속하세요."}, "yourVaultIsLockedV2": {"message": "당신의 보관함이 잠겼습니다."}, "yourAccountIsLocked": {"message": "당신의 계정이 잠겼습니다."}, "or": {"message": "또는"}, "unlock": {"message": "잠금 해제"}, "loggedInAsOn": {"message": "$HOSTNAME$ 에 $EMAIL$ 로 로그인했습니다.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "잘못된 마스터 비밀번호"}, "vaultTimeout": {"message": "보관함 시간 제한"}, "vaultTimeout1": {"message": "시간초과"}, "lockNow": {"message": "지금 잠그기"}, "lockAll": {"message": "모두 잠그기"}, "immediately": {"message": "즉시"}, "tenSeconds": {"message": "10초"}, "twentySeconds": {"message": "20초"}, "thirtySeconds": {"message": "30초"}, "oneMinute": {"message": "1분"}, "twoMinutes": {"message": "2분"}, "fiveMinutes": {"message": "5분"}, "fifteenMinutes": {"message": "15분"}, "thirtyMinutes": {"message": "30분"}, "oneHour": {"message": "1시간"}, "fourHours": {"message": "4시간"}, "onLocked": {"message": "시스템 잠금 시"}, "onRestart": {"message": "브라우저 다시 시작 시"}, "never": {"message": "안함"}, "security": {"message": "보안"}, "confirmMasterPassword": {"message": "마스터 비밀번호 확정"}, "masterPassword": {"message": "마스터 비밀번호"}, "masterPassImportant": {"message": "마스터 비밀번호는 잊어버려도 복구할 수 없습니다!"}, "masterPassHintLabel": {"message": "마스터 비밀번호 힌트"}, "errorOccurred": {"message": "오류가 발생했습니다"}, "emailRequired": {"message": "이메일은 반드시 입력해야 합니다."}, "invalidEmail": {"message": "잘못된 이메일 주소입니다."}, "masterPasswordRequired": {"message": "마스터 비밀번호가 필요합니다."}, "confirmMasterPasswordRequired": {"message": "마스터 비밀번호를 재입력해야 합니다."}, "masterPasswordMinlength": {"message": "마스터 비밀번호는 최소 $VALUE$자 이상이어야 합니다.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "마스터 비밀번호 확인과 마스터 비밀번호가 일치하지 않습니다."}, "newAccountCreated": {"message": "계정 생성이 완료되었습니다! 이제 로그인하실 수 있습니다."}, "newAccountCreated2": {"message": "계정 생성이 완료되었습니다!"}, "youHaveBeenLoggedIn": {"message": "로그인이 이미 되어있습니다."}, "youSuccessfullyLoggedIn": {"message": "로그인에 성공했습니다."}, "youMayCloseThisWindow": {"message": "이제 창을 닫으실 수 있습니다."}, "masterPassSent": {"message": "마스터 비밀번호 힌트가 담긴 이메일을 보냈습니다."}, "verificationCodeRequired": {"message": "인증 코드는 반드시 입력해야 합니다."}, "webauthnCancelOrTimeout": {"message": "인증이 너무 오래 걸리거나 취소되었습니다. 다시 시도하여 주십시오."}, "invalidVerificationCode": {"message": "유효하지 않은 확인 코드"}, "valueCopied": {"message": "$VALUE$를 클립보드에 복사함", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "선택한 항목을 이 페이지에서 자동 완성할 수 없습니다. 대신 정보를 직접 복사 / 붙여넣기하여 사용하십시오."}, "totpCaptureError": {"message": "현재 웹페이지에서 QR 코드를 스캔할 수 없습니다"}, "totpCaptureSuccess": {"message": "인증 키를 추가했습니다"}, "totpCapture": {"message": "현재 웹페이지에서 QR 코드 스캔하기"}, "totpHelperTitle": {"message": "간편하게 2단계 인증을 만들기"}, "totpHelper": {"message": "Bitwarden은 2단계 인증 코드들을 저장하고, 채워넣을 수 있습니다. 키를 복사하여 이 필드에 붙여넣으세요."}, "totpHelperWithCapture": {"message": "Bitwarden은 2단계 인증 코드들을 저장하고, 채워넣을 수 있습니다. 카메라 아이콘을 선택하고, 이 웹사이드의 인증 도구 QR코드를 스크린샷을 찍거나, 키를 복사하여 이 필드에 붙여넣으세요."}, "learnMoreAboutAuthenticators": {"message": "인증 도구에 대해 더 알아보기"}, "copyTOTP": {"message": "인증서 키 (TOTP) 복사"}, "loggedOut": {"message": "로그아웃됨"}, "loggedOutDesc": {"message": "계정이 로그아웃 되었습니다."}, "loginExpired": {"message": "로그인 세션이 만료되었습니다."}, "logIn": {"message": "로그인"}, "logInToBitwarden": {"message": "Bitwarden에 로그인"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "등록 재시작"}, "expiredLink": {"message": "만료된 링크"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "등록 재시작 혹은 다시 로그인을 해주시길 바랍니다"}, "youMayAlreadyHaveAnAccount": {"message": "계정을 이미 가지고 계실수도 있습니다."}, "logOutConfirmation": {"message": "정말 로그아웃하시겠습니까?"}, "yes": {"message": "예"}, "no": {"message": "아니오"}, "location": {"message": "Location"}, "unexpectedError": {"message": "예기치 못한 오류가 발생했습니다."}, "nameRequired": {"message": "이름은 반드시 입력해야 합니다."}, "addedFolder": {"message": "폴더 추가함"}, "twoStepLoginConfirmation": {"message": "2단계 인증은 보안 키, 인증 앱, SMS, 전화 통화 등의 다른 기기로 사용자의 로그인 시도를 검증하여 사용자의 계정을 더욱 안전하게 만듭니다. 2단계 인증은 bitwarden.com 웹 보관함에서 활성화할 수 있습니다. 지금 웹 사이트를 방문하시겠습니까?"}, "twoStepLoginConfirmationContent": {"message": "Bitwarden 웹 앱에 2단계 인증을 설정하여, 당신의 계정을 좀 더 안전하게 만드세요."}, "twoStepLoginConfirmationTitle": {"message": "웹 앱으로 진행하나요?"}, "editedFolder": {"message": "폴더 편집함"}, "deleteFolderConfirmation": {"message": "정말 이 폴더를 삭제하시겠습니까?"}, "deletedFolder": {"message": "폴더 삭제함"}, "gettingStartedTutorial": {"message": "시작하기 튜토리얼"}, "gettingStartedTutorialVideo": {"message": "브라우저 확장 프로그램을 최대한 활용하는 방법을 알아보려면 시작하기 튜토리얼을 확인하세요."}, "syncingComplete": {"message": "동기화 완료"}, "syncingFailed": {"message": "동기화 실패"}, "passwordCopied": {"message": "비밀번호를 클립보드에 복사함"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "새 URI"}, "addDomain": {"message": "도메인 추가", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "항목 추가함"}, "editedItem": {"message": "항목 편집함"}, "deleteItemConfirmation": {"message": "정말로 휴지통으로 이동시킬까요?"}, "deletedItem": {"message": "항목 삭제함"}, "overwritePassword": {"message": "비밀번호 덮어쓰기"}, "overwritePasswordConfirmation": {"message": "정말 현재 비밀번호를 덮어쓰시겠습니까?"}, "overwriteUsername": {"message": "아이디 덮어쓰기"}, "overwriteUsernameConfirmation": {"message": "정말 현재 아이디를 덮어쓰시겠습니까?"}, "searchFolder": {"message": "폴더 검색"}, "searchCollection": {"message": "컬렉션 검색"}, "searchType": {"message": "검색 유형"}, "noneFolder": {"message": "폴더 없음", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "로그인을 추가할 건지 물어보기"}, "vaultSaveOptionsTitle": {"message": "보관함 옵션들을 저장하기"}, "addLoginNotificationDesc": {"message": "\"로그인 추가 알림\"을 사용하면 새 로그인을 사용할 때마다 보관함에 그 로그인을 추가할 것인지 물어봅니다."}, "addLoginNotificationDescAlt": {"message": "보관함에 항목이 없을 경우 추가하라는 메시지를 표시합니다. 모든 로그인된 계정에 적용됩니다."}, "showCardsInVaultViewV2": {"message": "보관함 보기에서 언제나 카드 자동 완성 제안을 표시"}, "showCardsCurrentTab": {"message": "탭 페이지에 카드 표시"}, "showCardsCurrentTabDesc": {"message": "간편한 자동완성을 위해 탭에 카드 항목들을 나열"}, "showIdentitiesInVaultViewV2": {"message": "보관함 보기에서 언제나 신원의 자동 완성 제안을 표시"}, "showIdentitiesCurrentTab": {"message": "탭 페이지에 신원들을 표시"}, "showIdentitiesCurrentTabDesc": {"message": "간편한 자동완성을 위해 탭에 신원 항목들을 나열"}, "clickToAutofillOnVault": {"message": "보관함 보기에서 항목을 클릭하여 자동 완성"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "클립보드 비우기", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "자동으로 클립보드에 복사된 값을 제거합니다.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Bitwarden이 이 비밀번호를 기억하도록 하시겠습니까?"}, "notificationAddSave": {"message": "예, 지금 저장하겠습니다."}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "현재 로그인으로 업데이트할 건지 묻기"}, "changedPasswordNotificationDesc": {"message": "웹사이트에서 변경 사항이 감지되면 로그인 비밀번호를 업데이트하라는 메시지를 표시합니다."}, "changedPasswordNotificationDescAlt": {"message": "웹사이트에서 변경 사항이 감지되면 로그인 비밀번호를 업데이트하라는 메시지를 표시합니다. 모든 로그인된 계정에 적용됩니다."}, "enableUsePasskeys": {"message": "패스키를 저장 및 사용할지 묻기"}, "usePasskeysDesc": {"message": "보관함에 새 패스키를 저장하거나 로그인할지 물어봅니다. 모든 로그인된 계정에 적용됩니다."}, "notificationChangeDesc": {"message": "Bitwarden에 저장되어 있는 비밀번호를 이 비밀번호로 변경하시겠습니까?"}, "notificationChangeSave": {"message": "예, 지금 변경하겠습니다."}, "notificationUnlockDesc": {"message": "Bitwarden 보관함을 잠금 해제 하여 자동완성 요청을 완료하세요."}, "notificationUnlock": {"message": "잠금 해제"}, "additionalOptions": {"message": "추가 옵션"}, "enableContextMenuItem": {"message": "문맥 매뉴 옵션 표시"}, "contextMenuItemDesc": {"message": "우클릭을 사용하여, 비밀번호 생성과 웹사이트 로그인 매칭에 접근하세요"}, "contextMenuItemDescAlt": {"message": "우클릭을 사용하여, 웹사이트의 비밀번호 생성과 사용가능한 로그인들에 접근하세요. 모든 로그인 된 계정에 적용됩니다."}, "defaultUriMatchDetection": {"message": "기본 URI 일치 인식", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "자동 완성같은 작업을 수행할 때 로그인에 대해 URI 일치 감지가 처리되는 기본 방법을 선택하십시오."}, "theme": {"message": "테마"}, "themeDesc": {"message": "애플리케이션의 색상 테마를 변경합니다."}, "themeDescAlt": {"message": "애플리케이션 색상 테마를 변경합니다. 모든 로그인된 계정에 적용됩니다."}, "dark": {"message": "어두운 테마", "description": "Dark color"}, "light": {"message": "밝은 테마", "description": "Light color"}, "exportFrom": {"message": "~(으)로부터 내보내기"}, "exportVault": {"message": "보관함 내보내기"}, "fileFormat": {"message": "파일 형식"}, "fileEncryptedExportWarningDesc": {"message": "이 파일 내보내기는 비밀번호로 보호될 것이며, 파일을 해독하기 위해서는 파일 비밀번호가 필요합니다."}, "filePassword": {"message": "파일 비밀번호"}, "exportPasswordDescription": {"message": "이 비밀번호는 이 파일을 파일 내보내거나, 가져오는데 사용됩니다."}, "accountRestrictedOptionDescription": {"message": "내보내기를 당신의 계정의 사용자이름과 마스터비밀번호로부터 파생된 계정 암호화 키를 사용하여 암호화하고, 현재의 Bitwarden 계정으로만 가져오도록 제한합니다."}, "passwordProtectedOptionDescription": {"message": "파일에 비밀번호를 설정하여 내보내기를 암호화하고, 어느 Bitwarden 계정으로든 그 비밀번호로 해독하여 가져오기 합니다."}, "exportTypeHeading": {"message": "내보내기 유형"}, "accountRestricted": {"message": "계정 제한됨"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "파일 비밀번호와 파일 비밀번호 확인이 일치하지 않습니다."}, "warning": {"message": "경고", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "경고", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "보관함 내보내기 확인"}, "exportWarningDesc": {"message": "내보내기는 보관함 데이터가 암호화되지 않은 형식으로 포함됩니다. 내보낸 파일을 안전하지 않은 채널(예: 이메일)을 통해 저장하거나 보내지 마십시오. 사용이 끝난 후에는 즉시 삭제하십시오."}, "encExportKeyWarningDesc": {"message": "이 내보내기는 계정의 암호화 키를 사용하여 데이터를 암호화합니다. 추후 계정의 암호화 키를 교체할 경우 다시 내보내기를 진행해야 합니다. 그러지 않을 경우 이 내보내기 파일을 해독할 수 없게 됩니다."}, "encExportAccountWarningDesc": {"message": "모든 Bitwarden 사용자 계정은 고유한 계정 암호화 키를 가지고 있습니다. 따라서, 다른 계정에서는 암호화된 내보내기를 가져올 수 없습니다."}, "exportMasterPassword": {"message": "보관함 데이터를 내보내려면 마스터 비밀번호를 입력하세요."}, "shared": {"message": "공유됨"}, "bitwardenForBusinessPageDesc": {"message": "비지니스용 Bitwarden은 조직을 사용하여 보관함 항목들을 다른 사람과 공유할 수 있게 해줍니다. 자세한 내용은 bitwarden.com 사이트에서 확인해주세요"}, "moveToOrganization": {"message": "조직으로 이동하기"}, "movedItemToOrg": {"message": "$ITEMNAME$이(가) $ORGNAME$(으)로 이동됨", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "이 항목을 이동할 조직을 선택하십시오. 항목이 조직으로 이동되면 소유권이 조직으로 이전됩니다. 일단 이동되면, 더는 이동된 항목의 직접적인 소유자가 아니게 됩니다."}, "learnMore": {"message": "더 알아보기"}, "authenticatorKeyTotp": {"message": "인증 키 (TOTP)"}, "verificationCodeTotp": {"message": "인증 코드 (TOTP)"}, "copyVerificationCode": {"message": "인증 코드 복사"}, "attachments": {"message": "첨부 파일"}, "deleteAttachment": {"message": "첨부 파일 삭제"}, "deleteAttachmentConfirmation": {"message": "정말 이 첨부 파일을 삭제하시겠습니까?"}, "deletedAttachment": {"message": "첨부 파일 삭제함"}, "newAttachment": {"message": "새 첨부 파일 추가"}, "noAttachments": {"message": "첨부 파일이 없습니다."}, "attachmentSaved": {"message": "첨부 파일을 저장했습니다."}, "file": {"message": "파일"}, "fileToShare": {"message": "공유할 파일"}, "selectFile": {"message": "파일을 선택하세요."}, "maxFileSize": {"message": "최대 파일 크기는 500MB입니다."}, "featureUnavailable": {"message": "기능 사용할 수 없음"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "프리미엄 멤버십"}, "premiumManage": {"message": "멤버십 관리"}, "premiumManageAlert": {"message": "bitwarden.com 웹 보관함에서 멤버십을 관리할 수 있습니다. 지금 웹 사이트를 방문하시겠습니까?"}, "premiumRefresh": {"message": "멤버십 새로 고침"}, "premiumNotCurrentMember": {"message": "프리미엄 사용자가 아닙니다."}, "premiumSignUpAndGet": {"message": "프리미엄 멤버십에 가입하면 얻을 수 있는 것:"}, "ppremiumSignUpStorage": {"message": "1GB의 암호화된 파일 저장소."}, "premiumSignUpEmergency": {"message": "비상 접근"}, "premiumSignUpTwoStepOptions": {"message": "YubiKey나 Duo와 같은 독점적인 2단계 로그인 옵션"}, "ppremiumSignUpReports": {"message": "보관함을 안전하게 유지하기 위한 암호 위생, 계정 상태, 데이터 유출 보고서"}, "ppremiumSignUpTotp": {"message": "보관함에 등록된 로그인 항목을 위한 TOTP 인증 코드(2FA) 생성기."}, "ppremiumSignUpSupport": {"message": "고객 지원 우선 순위 제공."}, "ppremiumSignUpFuture": {"message": "앞으로 추가될 모든 프리미엄 기능을 사용할 수 있습니다. 기대하세요!"}, "premiumPurchase": {"message": "프리미엄 멤버십 구입"}, "premiumPurchaseAlertV2": {"message": "Bitwarden 웹 앱의 계정 설정에서 프리미엄에 대한 결제를 할 수 있습니다."}, "premiumCurrentMember": {"message": "프리미엄 사용자입니다!"}, "premiumCurrentMemberThanks": {"message": "Bitwarden을 지원해 주셔서 감사합니다."}, "premiumFeatures": {"message": "프리미엄으로 업그래이드 하고 받기: "}, "premiumPrice": {"message": "이 모든 기능을 연 $PRICE$에 이용하실 수 있습니다!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "이 모든 기능을 연 $PRICE$에 이용하실 수 있습니다!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "새로 고침 완료"}, "enableAutoTotpCopy": {"message": "인증 코드 자동으로 복사하기"}, "disableAutoTotpCopyDesc": {"message": "로그인에 인증 키가 연결되어 있을 경우, 그 로그인을 자동 완성할 때마다 TOTP 인증 코드가 클립보드에 자동으로 복사됩니다."}, "enableAutoBiometricsPrompt": {"message": "실행 시 생체 인증 요구하기"}, "premiumRequired": {"message": "프리미엄 멤버십 필요"}, "premiumRequiredDesc": {"message": "이 기능을 사용하려면 프리미엄 멤버십이 필요합니다."}, "authenticationTimeout": {"message": "인증 시간 초과"}, "authenticationSessionTimedOut": {"message": "인증 세션 시간이 초과 되었습니다. 다시 로그인을 시작해주세요."}, "verificationCodeEmailSent": {"message": "$EMAIL$ 주소로 인증 이메일을 보냈습니다.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "보안 키를 컴퓨터의 USB 포트에 삽입하고 버튼이 있는 경우 누르세요."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "WebAuthn 인증"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "로그인 불가능"}, "noTwoStepProviders": {"message": "이 계정은 2단계 인증을 사용합니다. 그러나 설정된 2단계 인증 중 이 웹 브라우저에서 지원하는 방식이 없습니다."}, "noTwoStepProviders2": {"message": "지원하는 웹 브라우저(Chrome 등)를 사용하거나 더 많은 브라우저를 지원하는 2단계 인증 방식(인증 앱 등)을 추가하세요."}, "twoStepOptions": {"message": "2단계 인증 옵션"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "모든 2단계 인증을 사용할 수 없는 상황인가요? 복구 코드를 사용하여 계정의 모든 2단계 인증을 비활성화할 수 있습니다."}, "recoveryCodeTitle": {"message": "복구 코드"}, "authenticatorAppTitle": {"message": "인증 앱"}, "authenticatorAppDescV2": {"message": "Bitwarden 인증같은 인증 앱을 통해 코드를 생성하여 입력해주세요", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "YubiKey OTP 보안 키"}, "yubiKeyDesc": {"message": "YubiKey를 사용하여 사용자의 계정에 접근합니다. YubiKey 4, 4 Nano, 4C 및 NEO 기기를 사용할 수 있습니다."}, "duoDescV2": {"message": "Duo Security에서 생성한 코드를 입력하세요", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Duo Mobile 앱, SMS, 전화 통화를 사용한 조직용 Duo Security 또는 U2F 보안 키를 사용하여 인증하세요.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "WebAuthn이 활성화된 보안 키를 사용하여 계정에 접근하세요."}, "emailTitle": {"message": "이메일"}, "emailDescV2": {"message": "이메일로 전송된 코드를 입력하세요."}, "selfHostedEnvironment": {"message": "자체 호스팅 환경"}, "selfHostedBaseUrlHint": {"message": "온-프레미스 Bitwarden이 호스팅되고 있는 서버의 기본 URL을 지정하세요. 예: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "고급 구성의 경우 각 서비스의 기본 URL을 독립적으로 지정할 수 있습니다."}, "selfHostedEnvFormInvalid": {"message": "기본 서버 URL이나 최소한 하나의 사용자 지정 환경을 추가해야 합니다."}, "customEnvironment": {"message": "사용자 지정 환경"}, "baseUrl": {"message": "서버 URL"}, "selfHostBaseUrl": {"message": "자체 호스트 서버 URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API 서버 URL"}, "webVaultUrl": {"message": "웹 보관함 서버 URL"}, "identityUrl": {"message": "ID 서버 URL"}, "notificationsUrl": {"message": "알림 서버 URL"}, "iconsUrl": {"message": "아이콘 서버 URL"}, "environmentSaved": {"message": "환경 URL 값을 저장했습니다."}, "showAutoFillMenuOnFormFields": {"message": "입력 필드에 자동 완성 메뉴 표시", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "자동 완성 제안"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "양식 필드에 자동 완성 제안 표시"}, "showInlineMenuIdentitiesLabel": {"message": "신원를 제안으로 표시"}, "showInlineMenuCardsLabel": {"message": "카드를 제안으로 표시"}, "showInlineMenuOnIconSelectionLabel": {"message": "아이콘을 선택할 때 제안을 표시"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "로그인한 모든 계정에 적용"}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "충돌을 방지하기 위해 브라우저의 기본 암호 관리 설정을 해제합니다."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "브라우저 설정 편집"}, "autofillOverlayVisibilityOff": {"message": "끄기", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "필드가 선택되었을 때 (포커스 상태)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "자동 완성 아이콘이 선택되었을 때", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "페이지 로드 시 자동 완성"}, "enableAutoFillOnPageLoad": {"message": "페이지 로드 시 자동 완성 사용"}, "enableAutoFillOnPageLoadDesc": {"message": "로그인 양식을 감지하면 웹 페이지 로드 시 자동 완성을 자동으로 수행합니다."}, "experimentalFeature": {"message": "취약하거나 신뢰할 수 없는 웹사이트 페이지 로드 시 자동 완성이 악용될 수 있습니다."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "위험에 대해 자세히 알아보기"}, "learnMoreAboutAutofill": {"message": "자동 완정에 대해 자세히 할아보기"}, "defaultAutoFillOnPageLoad": {"message": "로그인 항목에 대한 기본 자동 완성 설정"}, "defaultAutoFillOnPageLoadDesc": {"message": "페이지 로드 시 자동 완성을 켠 뒤에는 각 로그인 항목별로 이 기능을 켜거나 끌 수 있습니다. 이 옵션은 해당 기능을 개별적으로 구성하지 않은 항목에 사용되는 기본 설정값입니다."}, "itemAutoFillOnPageLoad": {"message": "페이지 로드 시 자동 완성 (옵션에서 켜져 있을 경우)"}, "autoFillOnPageLoadUseDefault": {"message": "기본 설정 사용"}, "autoFillOnPageLoadYes": {"message": "페이지 로드 시 자동 완성"}, "autoFillOnPageLoadNo": {"message": "페이지 로드 시 자동 완성 안 함"}, "commandOpenPopup": {"message": "보관함 팝업 열기"}, "commandOpenSidebar": {"message": "사이드바에서 보관함 열기"}, "commandAutofillLoginDesc": {"message": "현재 웹사이트에 마지막으로 사용된 로그인을 자동 채우기"}, "commandAutofillCardDesc": {"message": "현재 웹사이트에 마지막으로 사용된 카드를 자동 채우기"}, "commandAutofillIdentityDesc": {"message": "현재 웹사이트에 마지막으로 사용된 신원을 자동 채우기"}, "commandGeneratePasswordDesc": {"message": "새 무작위 비밀번호를 만들고 클립보드에 복사합니다."}, "commandLockVaultDesc": {"message": "보관함 잠그기"}, "customFields": {"message": "사용자 지정 필드"}, "copyValue": {"message": "값 복사"}, "value": {"message": "값"}, "newCustomField": {"message": "새 사용자 지정 필드"}, "dragToSort": {"message": "드래그하여 정렬"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "텍스트"}, "cfTypeHidden": {"message": "숨김"}, "cfTypeBoolean": {"message": "참 / 거짓"}, "cfTypeCheckbox": {"message": "체크박스"}, "cfTypeLinked": {"message": "연결됨", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "연결된 값", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "인증 코드가 담긴 이메일을 확인하기 위해 팝업 창의 바깥쪽을 누르면 이 팝업이 닫힙니다. 팝업 창이 닫히는 것을 방지하기 위해 이 팝업을 새 창에서 다시 여시겠습니까?"}, "popupU2fCloseMessage": {"message": "이 브라우저의 팝업 창에서는 U2F 요청을 처리할 수 없습니다. U2F로 로그인할 수 있도록 이 창을 새 창에서 여시겠습니까?"}, "enableFavicon": {"message": "웹사이트 아이콘 표시하기"}, "faviconDesc": {"message": "로그인 정보 옆에 식별용 이미지를 표시합니다."}, "faviconDescAlt": {"message": "각 로그인 정보 옆에 인식할 수 있는 이미지를 표시합니다. 모든 로그인된 계정에 적용됩니다."}, "enableBadgeCounter": {"message": "배지 갯수 표시"}, "badgeCounterDesc": {"message": "현재 웹 페이지에 저장된 로그인 정보의 수를 표시합니다."}, "cardholderName": {"message": "카드 소유자 이름"}, "number": {"message": "번호"}, "brand": {"message": "브랜드"}, "expirationMonth": {"message": "만료 월"}, "expirationYear": {"message": "만료 연도"}, "expiration": {"message": "만료"}, "january": {"message": "1월"}, "february": {"message": "2월"}, "march": {"message": "3월"}, "april": {"message": "4월"}, "may": {"message": "5월"}, "june": {"message": "6월"}, "july": {"message": "7월"}, "august": {"message": "8월"}, "september": {"message": "9월"}, "october": {"message": "10월"}, "november": {"message": "11월"}, "december": {"message": "12월"}, "securityCode": {"message": "보안 코드"}, "ex": {"message": "예)"}, "title": {"message": "제목"}, "mr": {"message": "Mr"}, "mrs": {"message": "Mrs"}, "ms": {"message": "Ms"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "이름"}, "middleName": {"message": "가운데 이름"}, "lastName": {"message": "성"}, "fullName": {"message": "전체 이름"}, "identityName": {"message": "ID 이름"}, "company": {"message": "회사"}, "ssn": {"message": "주민등록번호"}, "passportNumber": {"message": "여권 번호"}, "licenseNumber": {"message": "면허 번호"}, "email": {"message": "이메일"}, "phone": {"message": "전화번호"}, "address": {"message": "주소"}, "address1": {"message": "주소 1"}, "address2": {"message": "주소 2"}, "address3": {"message": "주소 3"}, "cityTown": {"message": "읍 / 면 / 동"}, "stateProvince": {"message": "시 / 도"}, "zipPostalCode": {"message": "우편번호"}, "country": {"message": "국가"}, "type": {"message": "유형"}, "typeLogin": {"message": "로그인"}, "typeLogins": {"message": "로그인"}, "typeSecureNote": {"message": "보안 메모"}, "typeCard": {"message": "카드"}, "typeIdentity": {"message": "신원"}, "typeSshKey": {"message": "SSH 키"}, "newItemHeader": {"message": "새 $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "$TYPE$ 수정", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "$TYPE$ 보기", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "비밀번호 변경 기록"}, "generatorHistory": {"message": "생성기 기록"}, "clearGeneratorHistoryTitle": {"message": "생성기 기록 지우기"}, "cleargGeneratorHistoryDescription": {"message": "계속하면 모든 항목이 생성기 기록에서 영구적으로 삭제됩니다. 계속하시겠습니까?"}, "back": {"message": "뒤로"}, "collections": {"message": "컬렉션"}, "nCollections": {"message": "$COUNT$ 컬렉션", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "즐겨찾기"}, "popOutNewWindow": {"message": "새 창에서 보기"}, "refresh": {"message": "새로 고침"}, "cards": {"message": "카드"}, "identities": {"message": "신원"}, "logins": {"message": "로그인"}, "secureNotes": {"message": "보안 메모"}, "sshKeys": {"message": "SSH 키"}, "clear": {"message": "삭제", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "비밀번호가 노출되었는지 확인합니다."}, "passwordExposed": {"message": "이 비밀번호는 데이터 유출에 $VALUE$회 노출되었습니다. 비밀번호를 변경하는 것이 좋습니다.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "이 비밀번호는 데이터 유출 목록에 없습니다. 사용하기에 안전한 비밀번호입니다."}, "baseDomain": {"message": "기본 도메인", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "기본 도메인 (추천)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "도메인 이름", "description": "Domain name. Ex. website.com"}, "host": {"message": "호스트", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "정확히 일치"}, "startsWith": {"message": "...으로 시작"}, "regEx": {"message": "정규 표현식", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "일치 인식", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "기본 일치 인식", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "표시 / 숨기기"}, "toggleCurrentUris": {"message": "현재 URI 전환", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "현재 URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "조직", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "유형"}, "allItems": {"message": "모든 항목"}, "noPasswordsInList": {"message": "비밀번호가 없습니다."}, "clearHistory": {"message": "기록 지우기"}, "nothingToShow": {"message": "항목 없음"}, "nothingGeneratedRecently": {"message": "최근에 생성한 것이 없습니다"}, "remove": {"message": "제거"}, "default": {"message": "기본값"}, "dateUpdated": {"message": "업데이트됨", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "생성됨", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "비밀번호 업데이트됨", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "정말 \"잠그지 않음\" 옵션을 사용하시겠습니까? 잠금 옵션을 \"잠그지 않음\"으로 설정하면 사용자 보관함의 암호화 키를 사용자의 기기에 보관합니다. 이 옵션을 사용하기 전에 사용자의 기기가 잘 보호되어 있는 상태인지 확인하십시오."}, "noOrganizationsList": {"message": "속해 있는 조직이 없습니다. 조직에 속하면 다른 사용자들과 항목을 안전하게 공유할 수 있습니다."}, "noCollectionsInList": {"message": "컬렉션이 없습니다."}, "ownership": {"message": "소유자"}, "whoOwnsThisItem": {"message": "이 항목의 소유자는 누구입니까?"}, "strong": {"message": "강함", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "좋음", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "취약", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "취약한 마스터 비밀번호"}, "weakMasterPasswordDesc": {"message": "선택한 마스터 비밀번호는 취약합니다. Bitwarden 계정을 확실히 보호하려면 강력한 마스터 비밀번호(혹은 패스프레이즈)를 사용해야 합니다. 정말 이 마스터 비밀번호를 사용하시겠습니까?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "PIN 코드를 사용하여 잠금 해제"}, "setYourPinTitle": {"message": "PIN 설정"}, "setYourPinButton": {"message": "PIN 설정"}, "setYourPinCode": {"message": "Bitwarden 잠금해제에 사용될 PIN 코드를 설정합니다. 이 애플리케이션에서 완전히 로그아웃할 경우 PIN 설정이 초기화됩니다."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "PIN 코드가 필요합니다."}, "invalidPin": {"message": "잘못된 PIN 코드입니다."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "잘못된 PIN 입력 시도가 너무 많습니다. 로그아웃 합니다."}, "unlockWithBiometrics": {"message": "생체 인식을 사용하여 잠금 해제"}, "unlockWithMasterPassword": {"message": "마스터 비밀번호로 잠금 해제"}, "awaitDesktop": {"message": "데스크톱으로부터의 확인을 대기 중"}, "awaitDesktopDesc": {"message": "브라우저 생체 인식을 사용하기 위해서는 Bitwarden 데스크톱 앱에서 생체 인식을 이용하여 승인해주세요."}, "lockWithMasterPassOnRestart": {"message": "브라우저 다시 시작 시 마스터 비밀번호로 잠금"}, "lockWithMasterPassOnRestart1": {"message": "브라우저 다시 시작 시 마스터 비밀번호가 필요합니다"}, "selectOneCollection": {"message": "반드시 하나 이상의 컬렉션을 선택해야 합니다."}, "cloneItem": {"message": "항목 복제"}, "clone": {"message": "복제"}, "passwordGenerator": {"message": "비밀번호 생성기"}, "usernameGenerator": {"message": "사용자 이름 생성기"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "이 비밀번호 사용"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "이 사용자 이름 사용"}, "securePasswordGenerated": {"message": "보안 비밀번호가 생성되었습니다! 웹사이트에서 비밀번호를 업데이트하는 것도 잊지 마세요."}, "useGeneratorHelpTextPartOne": {"message": "생성기를 사용하여", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "강력한 고유 비밀번호를 만드세요", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "보관함 시간 제한 초과시 동작"}, "vaultTimeoutAction1": {"message": "시간초과 시 행동"}, "lock": {"message": "잠금", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "휴지통", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "휴지통 검색"}, "permanentlyDeleteItem": {"message": "영구적으로 항목 삭제"}, "permanentlyDeleteItemConfirmation": {"message": "정말로 이 항목을 영구적으로 삭제하시겠습니까?"}, "permanentlyDeletedItem": {"message": "영구적으로 삭제된 항목"}, "restoreItem": {"message": "항목 복원"}, "restoredItem": {"message": "복원된 항목"}, "alreadyHaveAccount": {"message": "이미 계정이 있으신가요?"}, "vaultTimeoutLogOutConfirmation": {"message": "로그아웃하면 보관함에 대한 모든 접근이 제거되며 시간 제한을 초과하면 온라인 인증을 요구합니다. 정말로 이 설정을 사용하시겠습니까?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "시간 제한 초과시 동작 확인"}, "autoFillAndSave": {"message": "자동 완성 및 저장"}, "fillAndSave": {"message": "채우기 및 저장"}, "autoFillSuccessAndSavedUri": {"message": "항목을 자동 완성하고 URI를 저장함"}, "autoFillSuccess": {"message": "항목을 자동 완성함"}, "insecurePageWarning": {"message": "경고: 이 페이지는 보안이 해제된 HTTP 페이지이며, 제출한 모든 정보는 다른 사람이 보고 변경할 수 있습니다. 이 로그인은 원래 보안(HTTPS) 페이지에 저장되었습니다."}, "insecurePageWarningFillPrompt": {"message": "여전히 이 로그인을 채우시겠습니까?"}, "autofillIframeWarning": {"message": "양식은 저장된 로그인의 URI가 아닌 다른 도메인에서 호스팅됩니다. 그래도 자동 완성을 사용하시려면 OK, 아니라면 취소 버튼을 선택해주세요."}, "autofillIframeWarningTip": {"message": "향후 이 경고를 방지하려면 이 URI인 $HOSTNAME$(을)를 Bitwarden로그인 항목에 저장하세요.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "마스터 비밀번호 설정"}, "currentMasterPass": {"message": "현재 마스터 비밀번호"}, "newMasterPass": {"message": "새 마스터 비밀번호"}, "confirmNewMasterPass": {"message": "새 마스터 비밀번호 확인"}, "masterPasswordPolicyInEffect": {"message": "하나 이상의 단체 정책이 마스터 비밀번호가 다음 사항을 따르도록 요구합니다:"}, "policyInEffectMinComplexity": {"message": "최소 복잡도 점수 $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "최소 길이 $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "하나 이상의 대문자 포함"}, "policyInEffectLowercase": {"message": "하나 이상의 소문자 포함"}, "policyInEffectNumbers": {"message": "하나 이상의 숫자 포함"}, "policyInEffectSpecial": {"message": "하나 이상의 특수문자 포함 $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "새 마스터 비밀번호가 정책 요구 사항을 따르지 않습니다."}, "receiveMarketingEmailsV2": {"message": "Email 받은 편지함을 통해 Bitwarden의 조언, 공지사항 및 연구 기회들을 얻어보세요"}, "unsubscribe": {"message": "구독 취소"}, "atAnyTime": {"message": "언제든지"}, "byContinuingYouAgreeToThe": {"message": "계속하면 다음에 동의하게 됩니다"}, "and": {"message": "그리고"}, "acceptPolicies": {"message": "이 박스를 체크하면 다음에 동의하는 것으로 간주됩니다:"}, "acceptPoliciesRequired": {"message": "서비스 약관 및 개인 정보 보호 정책을 확인하지 않았습니다."}, "termsOfService": {"message": "서비스 약관"}, "privacyPolicy": {"message": "개인 정보 보호 정책"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "비밀번호 힌트는 비밀번호와 같을 수 없습니다."}, "ok": {"message": "확인"}, "errorRefreshingAccessToken": {"message": "엑세스 토큰 새로고침 오류"}, "errorRefreshingAccessTokenDesc": {"message": "새로 고침 토큰이나 API 키를 찾을 수 없습니다. 로그아웃하고 다시 로그인해 주세요"}, "desktopSyncVerificationTitle": {"message": "데스크톱과의 동기화 인증"}, "desktopIntegrationVerificationText": {"message": "데스크톱 앱이 다음 지문 구절을 표시하는지 확인해주세요:"}, "desktopIntegrationDisabledTitle": {"message": "브라우저와 연결이 활성화되지 않았습니다"}, "desktopIntegrationDisabledDesc": {"message": "브라우저와 연결이 Bitwarden 데스크톱 앱에서 활성화되지 않았습니다. 데스크톱 앱의 설정에서 브라우저와 연결을 활성화해주세요."}, "startDesktopTitle": {"message": "Bitwarden 데스크톱 앱을 실행하세요"}, "startDesktopDesc": {"message": "이 기능을 사용하기 위해서는 Bitwarden 데스크톱 앱이 먼저 실행되어 있어야 합니다."}, "errorEnableBiometricTitle": {"message": "생체 인식을 활성화할 수 없음"}, "errorEnableBiometricDesc": {"message": "데스크톱 앱에서 작업이 취소되었습니다"}, "nativeMessagingInvalidEncryptionDesc": {"message": "데스크톱 앱에서 이 보안 통신 채널을 무효화했습니다. 다시 시도해 주세요."}, "nativeMessagingInvalidEncryptionTitle": {"message": "데스크톱과의 통신이 중단됨"}, "nativeMessagingWrongUserDesc": {"message": "데스크톱 앱에 다른 계정으로 로그인된 상태입니다. 두 앱에 같은 계정으로 로그인되어 있는지 확인해주세요."}, "nativeMessagingWrongUserTitle": {"message": "계정이 일치하지 않음"}, "nativeMessagingWrongUserKeyTitle": {"message": "생체인식 키 불일치"}, "nativeMessagingWrongUserKeyDesc": {"message": "생체 인식 잠금 해제에 실패했습니다. 생체 인식 비밀 키가 보관함 잠금 해제에 실패했습니다. 생체 인식을 다시 설정해 보세요."}, "biometricsNotEnabledTitle": {"message": "생체 인식이 활성화되지 않음"}, "biometricsNotEnabledDesc": {"message": "브라우저에서 생체 인식을 사용하기 위해서는 설정에서 데스크톱 생체 인식을 먼저 활성화해야 합니다."}, "biometricsNotSupportedTitle": {"message": "생체 인식이 지원되지 않음"}, "biometricsNotSupportedDesc": {"message": "이 기기에서는 생체 인식이 지원되지 않습니다."}, "biometricsNotUnlockedTitle": {"message": "사용자 잠금 또는 로그아웃"}, "biometricsNotUnlockedDesc": {"message": "데스크톱 애플리케이션에서 이 사용자의 잠금을 해제하고 다시 시도해 주세요."}, "biometricsNotAvailableTitle": {"message": "생체 인식 잠금 해제 사용 불가"}, "biometricsNotAvailableDesc": {"message": "생체 인식 잠금 해제는 현재 사용할 수 없습니다. 나중에 다시 시도해 주세요."}, "biometricsFailedTitle": {"message": "생체 인식 실패"}, "biometricsFailedDesc": {"message": "생체 인식을 완료할 수 없습니다. 마스터 비밀번호를 사용하거나 로그아웃하는 것을 고려하세요. 이 문제가 계속되면 Bitwarden 지원팀에 문의해 주세요."}, "nativeMessaginPermissionErrorTitle": {"message": "권한이 부여되지 않음"}, "nativeMessaginPermissionErrorDesc": {"message": "Bitwarden 데스크톱 앱과 통신할 권한이 부여되지 않은 상태에서는 브라우저 확장 프로그램에서 생체 인식을 사용할 수 없습니다. 다시 시도해 주세요."}, "nativeMessaginPermissionSidebarTitle": {"message": "권한 요청 오류"}, "nativeMessaginPermissionSidebarDesc": {"message": "사이드바에서는 이 동작을 수행할 수 없습니다. 팝업 혹은 새 창으로 열고 다시 시도해 주세요."}, "personalOwnershipSubmitError": {"message": "엔터프라이즈 정책으로 인해 개인 보관함에 항목을 저장할 수 없습니다. 조직에서 소유권 설정을 변경한 다음, 사용 가능한 컬렉션 중에서 선택해주세요."}, "personalOwnershipPolicyInEffect": {"message": "조직의 정책이 소유권 설정에 영향을 미치고 있습니다."}, "personalOwnershipPolicyInEffectImports": {"message": "조직 정책으로 인해 개별 보관함으로 항목을 가져오는 것이 차단되었습니다."}, "domainsTitle": {"message": "도메인", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "제외된 도메인"}, "excludedDomainsDesc": {"message": "Bitwarden은 이 도메인들에 대해 로그인 정보를 저장할 것인지 묻지 않습니다. 페이지를 새로고침해야 변경된 내용이 적용됩니다."}, "excludedDomainsDescAlt": {"message": "BItwarden은 로그인한 모든 계정에 대해 이러한 도메인에 대한 로그인 세부 정보를 저장하도록 요청하지 않습니다. 변경 사항을 적용하려면 페이지를 새로 고쳐야 합니다"}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "웹사이트 $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ 도메인은 유효한 도메인이 아닙니다.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "제외된 도메인 변경 사항 저장됨"}, "limitSendViews": {"message": "제한 보기"}, "limitSendViewsHint": {"message": "제한에 도달한 후에는 아무도 이 전송을 볼 수 없습니다.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "남은 $ACCESSCOUNT$ 횟수", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "보내기", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "보내기 세부 정보", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "텍스트"}, "sendTypeTextToShare": {"message": "공유할 텍스트"}, "sendTypeFile": {"message": "파일"}, "allSends": {"message": "모든 Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "기본적으로 텍스트 숨기기"}, "expired": {"message": "만료됨"}, "passwordProtected": {"message": "비밀번호로 보호됨"}, "copyLink": {"message": "링크 복사"}, "copySendLink": {"message": " Send 링크 복사", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "비밀번호 제거"}, "delete": {"message": "삭제"}, "removedPassword": {"message": "비밀번호 제거함"}, "deletedSend": {"message": " Send  삭제함", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "링크 보내기", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "비활성화됨"}, "removePasswordConfirmation": {"message": "비밀번호를 제거하시겠습니까?"}, "deleteSend": {"message": " Send 삭제", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "정말 이  Send를 삭제하시겠습니까?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "이  Send을 영구적으로 삭제하시겠습니까?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": " Send 편집", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "삭제 날짜"}, "deletionDateDescV2": {"message": "이  Send가 이 날짜에 영구적으로 삭제됩니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "만료 날짜"}, "oneDay": {"message": "1일"}, "days": {"message": "$DAYS$ 일", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "사용자 지정"}, "sendPasswordDescV3": {"message": "수신자가 이 Send에 액세스할 수 있도록 비밀번호 옵션를 추가합니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "새 Send 생성", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "새 비밀번호"}, "sendDisabled": {"message": "Send 비활성화됨", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "엔터프라이즈 정책으로 인해 이미 생성된 Send를 삭제하는 것만 허용됩니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send 생성함", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send가 성공적으로 생성되었습니다!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "이 Send는 링크가 있는 누구나 향후 1시간 동안 이용할 수 있습니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "이 전송은 링크가 있는 누구나 향후 $HOURS$ 시간 동안 이용할 수 있습니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "이 Send은 향후 1일 동안 링크가 있는 누구나 이용할 수 있습니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "이 Send은 향후 $DAYS$일 동안 링크가 있는 누구나 이용할 수 있습니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send 링크 복사됨", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send 수정됨", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "확장자를 새 창에서 열까요?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "파일 Send를 만들려면, 새 창으로 확장자를 열어야 합니다.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "파일을 선택하려면 이 배너를 클릭하여 확장 프로그램을 사이드바에서 열거나, 불가능한 경우 새 창에서 여세요."}, "sendFirefoxFileWarning": {"message": "Firefox에서 파일을 선택할 경우, 이 배너를 클릭하여 확장 프로그램을 사이드바 혹은 새 창에서 여세요."}, "sendSafariFileWarning": {"message": "Safari에서 파일을 선택할 경우, 이 배너를 클릭하여 확장 프로그램을 새 창에서 여세요."}, "popOut": {"message": "새 창에서 열기"}, "sendFileCalloutHeader": {"message": "시작하기 전에"}, "expirationDateIsInvalid": {"message": "제공된 만료 날짜가 유효하지 않습니다."}, "deletionDateIsInvalid": {"message": "제공된 삭제 날짜가 유효하지 않습니다."}, "expirationDateAndTimeRequired": {"message": "만료 날짜와 시간은 반드시 입력해야 합니다."}, "deletionDateAndTimeRequired": {"message": "삭제 날짜와 시간은 반드시 입력해야 합니다."}, "dateParsingError": {"message": "삭제 날짜와 만료 날짜를 저장하는 도중 오류가 발생했습니다."}, "hideYourEmail": {"message": "사람들로부터 이메일 주소를 숨기세요."}, "passwordPrompt": {"message": "마스터 비밀번호 재확인"}, "passwordConfirmation": {"message": "마스터 비밀번호 확인"}, "passwordConfirmationDesc": {"message": "이 작업은 보호되어 있습니다. 계속하려면 마스터 비밀번호를 입력하여 신원을 인증하세요."}, "emailVerificationRequired": {"message": "이메일 인증 필요함"}, "emailVerifiedV2": {"message": "이메일 인증됨"}, "emailVerificationRequiredDesc": {"message": "이 기능을 사용하려면 이메일 인증이 필요합니다. 웹 보관함에서 이메일을 인증할 수 있습니다."}, "updatedMasterPassword": {"message": "마스터 비밀번호 변경됨"}, "updateMasterPassword": {"message": "마스터 비밀번호 변경"}, "updateMasterPasswordWarning": {"message": "최근에 조직 관리자가 마스터 비밀번호를 변경했습니다. 보관함에 액세스하려면 지금 업데이트해야 합니다. 계속하면 현재 세션에서 로그아웃되며 다시 로그인해야 합니다. 다른 장치의 활성 세션은 최대 1시간 동안 계속 활성 상태로 유지될 수 있습니다."}, "updateWeakMasterPasswordWarning": {"message": "마스터 비밀번호가 조직 정책 중 하나 이상을 충족하지 못합니다. 보관함에 액세스하려면, 지금 마스터 비밀번호를 업데이트해야 합니다. 계속 진행하면 현재 세션에서 로그아웃되므로, 다시 로그인해야 합니다. 다른 장치에서 활성 세션은 최대 1시간 동안 계속 활성 상태로 유지될 수 있습니다."}, "tdeDisabledMasterPasswordRequired": {"message": "조직에서 신뢰할 수 있는 장치 암호화를 비활성화했습니다. 보관함에 접근하려면 마스터 비밀번호를 설정하세요."}, "resetPasswordPolicyAutoEnroll": {"message": "자동 등록"}, "resetPasswordAutoEnrollInviteWarning": {"message": "이 조직에는 자동으로 비밀번호 재설정에 등록하는 기업 정책이 있습니다. 등록하면 조직 관리자가 마스터 암호를 변경할 수 있습니다."}, "selectFolder": {"message": "폴더 선택..."}, "noFoldersFound": {"message": "폴더를 찾을 수 없습니다.", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "조직 권한이 업데이트되어 마스터 비밀번호를 설정해야 합니다.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "당신의 조직은 마스터 비밀번호를 설정해야 합니다.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "$TOTAL$ 중에서", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "인증 필요", "description": "Default title for the user verification dialog."}, "hours": {"message": "시"}, "minutes": {"message": "분"}, "vaultTimeoutPolicyAffectingOptions": {"message": "타임아웃 옵션에 기업의 정책 요구 사항이 적용되었습니다"}, "vaultTimeoutPolicyInEffect": {"message": "조직 정책이 보관함 제한 시간에 영향을 미치고 있습니다. 최대 허용 보관함 제한 시간은 $HOURS$시간 $MINUTES$분입니다", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "최대 $HOURS$시간 $MINUTES$분", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "타임아웃이 조직에서 설정한 제한을 초과합니다: 최대 $HOURS$시간 $MINUTES$분", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "조직 정책이 보관함 타임아웃에 영향을 미치고 있습니다. 최대 허용 보관함 타임아웃은 최대 $HOURS$시간 $MINUTES$분입니다. 보관함 타임아웃 작업은 $ACTION$으로 설정되어 있습니다.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "조직 정책에 따라 보관함 타임아웃 작업이 $ACTION$으로 설정되었습니다.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "보관함 시간 초과가 조직에서 설정한 제한을 초과합니다."}, "vaultExportDisabled": {"message": "보관함 내보내기 비활성화됨"}, "personalVaultExportPolicyInEffect": {"message": "하나 이상의 조직 정책이 개인 보관함을 내보내는 것을 제한하고 있습니다."}, "copyCustomFieldNameInvalidElement": {"message": "유효한 양식 요소를 식별하지 못했습니다. 대신 HTML을 검사해 보세요."}, "copyCustomFieldNameNotUnique": {"message": "고유 식별자를 찾을 수 없습니다."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "조직 나가기"}, "removeMasterPassword": {"message": "마스터 비밀번호 제거"}, "removedMasterPassword": {"message": "마스터 비밀번호가 제거되었습니다."}, "leaveOrganizationConfirmation": {"message": "정말 이 조직을 떠나시겠어요?"}, "leftOrganization": {"message": "조직을 떠났습니다."}, "toggleCharacterCount": {"message": "글자 수 표시하기"}, "sessionTimeout": {"message": "세션 시간이 초과되었습니다. 다시 로그인해주세요."}, "exportingPersonalVaultTitle": {"message": "개인 보관함을 내보내는 중"}, "exportingIndividualVaultDescription": {"message": "$EMAIL$과 관련된 개별 보관함 항목만 내보냅니다. 조직 보관함 항목은 포함되지 않습니다. 보관함 항목 정보만 내보내며 관련 첨부 파일은 포함되지 않습니다", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "조직 보관함을 내보내는 중"}, "exportingOrganizationVaultDesc": {"message": "$ORGANIZATION$ 조직과 연관된 조직 보관함만 내보내기됩니다. 개인 보관함이나 다른 조직의 항목은 포함되지 않습니다.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "오류"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "아이디 생성"}, "generateEmail": {"message": "이메일 생성"}, "spinboxBoundariesHint": {"message": "값은 $MIN$과 $MAX$ 사이여야 합니다", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " 강력한 비밀번호를 생성하려면 $RECOMMENDED$ 문자 이상을 사용하세요", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " 강력한 암호를 생성하려면 $RECOMMENDED$ 단어 이상을 사용하세요.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "추가 이메일", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "이메일 제공업체의 하위 주소 지정 기능을 사용하세요."}, "catchallEmail": {"message": "Catch-all 이메일 (도메인 상의 어떤 주소로도 전송된 이메일을 받을 수 있는 주소)"}, "catchallEmailDesc": {"message": "catch-all이 설정된 내 도메인의 메일함을 사용하세요."}, "random": {"message": "무작위"}, "randomWord": {"message": "무작위 단어"}, "websiteName": {"message": "웹사이트 이름"}, "service": {"message": "서비스"}, "forwardedEmail": {"message": "포워딩된 이메일 별칭"}, "forwardedEmailDesc": {"message": "외부 포워딩 서비스를 사용해서 이메일 주소 별칭을 만들어보세요."}, "forwarderDomainName": {"message": "이메일 도메인", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "선택한 서비스에서 지원하는 도메인 선택", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ 오류: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Bitwarden에서 생성됨", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "웹사이트: $WEBSITE$. Bitwarden에서 생성됨", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "잘못된 $SERVICENAME$ API 토큰\n", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "잘못된 $SERVICENAME$ API 토큰: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "$SERVICENAME$ 마스크된 이메일 계정 ID를 얻을 수 없습니다.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "잘못된 $SERVICENAME$ 도메인.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "잘못된 $SERVICENAME$ URL", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "알 수 없는 $SERVICENAME$ 오류가 발생했습니다.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "알 수 없는 포워더: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "호스트 이름", "description": "Part of a URL."}, "apiAccessToken": {"message": "API 액세스 토큰"}, "apiKey": {"message": "API 키"}, "ssoKeyConnectorError": {"message": "키 커넥터 오류: 키 커넥터가 사용 가능한지 및 정상적으로 작동하고 있는지 확인해주세요."}, "premiumSubcriptionRequired": {"message": "프리미엄 구독이 필요합니다"}, "organizationIsDisabled": {"message": "조직이 중지됨"}, "disabledOrganizationFilterError": {"message": "중단된 조직의 항목에 액세스할 수 없습니다. 조직 소유자에게 도움을 요청하세요."}, "loggingInTo": {"message": "$DOMAIN$(으)로 로그인", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "서버 버전"}, "selfHostedServer": {"message": "자체 호스팅"}, "thirdParty": {"message": "제 3자"}, "thirdPartyServerMessage": {"message": "제 3자 서버 구현에 연결되었습니다. $SERVERNAME$. 공식 서버를 사용하여 버그를 확인하거나 타사 서버에 보고해 주세요.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "확인된 날짜: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "마스터 비밀번호로 로그인"}, "newAroundHere": {"message": "새로 찾아오셨나요?"}, "rememberEmail": {"message": "이메일 기억하기"}, "loginWithDevice": {"message": "기기로 로그인"}, "fingerprintPhraseHeader": {"message": "지문 구절"}, "fingerprintMatchInfo": {"message": "반드시 보관함이 잠금 해제되었고, 지문 구절이 다른 기기에서 일치하는지 확인해주세요."}, "resendNotification": {"message": "알림 다시 보내기"}, "viewAllLogInOptions": {"message": "모든 로그인 방식 보기"}, "notificationSentDevice": {"message": "기기에 알림이 전송되었습니다."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "기기에 알림이 전송되었습니다."}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "요청이 승인되면 알림을 받게 됩니다"}, "needAnotherOptionV1": {"message": "다른 옵션이 필요하신가요?"}, "loginInitiated": {"message": "로그인 시작"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "노출된 마스터 비밀번호"}, "exposedMasterPasswordDesc": {"message": "데이터 유출이 된 비밀번호임이 발견되었습니다. 계정을 보호하려면 고유한 비밀번호를 사용하세요. 노출된 비밀번호를 사용하시겠습니까?"}, "weakAndExposedMasterPassword": {"message": "취약하고 노출된 마스터 비밀번호"}, "weakAndBreachedMasterPasswordDesc": {"message": "데이터 유출이 된 약한 비밀번호임이 발견되었습니다. 계정을 보호하려면 강력하고 고유한 비밀번호를 사용하세요. 이 비밀번호를 사용하시겠습니까?"}, "checkForBreaches": {"message": "이 비밀번호에 대한 알려진 데이터 유출 확인\n"}, "important": {"message": "중요:"}, "masterPasswordHint": {"message": "마스터 비밀번호를 잊어버리면 복구할 수 없습니다!\n"}, "characterMinimum": {"message": "최소 $LENGTH$ 문자", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "조직 정책에 따라, 페이지 로드 시 자동 완성 기능을 켰습니다."}, "howToAutofill": {"message": "자동 완성 사용법"}, "autofillSelectInfoWithCommand": {"message": "이 화면에서 항목을 선택하거나, 바로 가기 $COMMAND$를 사용하거나, 설정의 다른 옵션을 탐색하세요.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "이 화면에서 항목을 선택하거나 설정의 다른 옵션을 탐색하세요."}, "gotIt": {"message": "이해했습니다"}, "autofillSettings": {"message": "자동 완성 설정"}, "autofillKeyboardShortcutSectionTitle": {"message": "자동 완성 바로가기"}, "autofillKeyboardShortcutUpdateLabel": {"message": "바로가기 변경"}, "autofillKeyboardManagerShortcutsLabel": {"message": "바로가기 관리"}, "autofillShortcut": {"message": "자동 완성 키보드 단축키"}, "autofillLoginShortcutNotSet": {"message": "자동 채우기 로그인 바로 가기가 설정되어 있지 않습니다. 브라우저 설정에서 이 항목을 변경해주세요."}, "autofillLoginShortcutText": {"message": "자동 채우기 로그인 바로 가기는 $COMMAND$입니다. 브라우저 설정의 모든 바로 가기를 관리하세요.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "기본 자동 완성 바로 가기: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "새 창에서 열립니다"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "향후 로그인을 원활하게 하기 위해 이 기기 기억하기"}, "deviceApprovalRequired": {"message": "기기 승인이 필요합니다. 아래에서 승인 옵션을 선택하세요:"}, "deviceApprovalRequiredV2": {"message": "기기 승인이 필요합니다."}, "selectAnApprovalOptionBelow": {"message": "아래에서 승인 옵션을 선택하세요"}, "rememberThisDevice": {"message": "이 기기 기억하기"}, "uncheckIfPublicDevice": {"message": "공용 기기를 사용하는 경우 체크 해제"}, "approveFromYourOtherDevice": {"message": "다른 장치에서 승인"}, "requestAdminApproval": {"message": "관리자 인증 필요"}, "ssoIdentifierRequired": {"message": "조직의 SSO 식별자가 필요합니다"}, "creatingAccountOn": {"message": "계정 만들기"}, "checkYourEmail": {"message": "이메일을 확인해주세요"}, "followTheLinkInTheEmailSentTo": {"message": "이메일로 전송한 링크를 통해"}, "andContinueCreatingYourAccount": {"message": "계정을 계속 생성하세요."}, "noEmail": {"message": "이메일이 전송되지 않았나요?"}, "goBack": {"message": "뒤로 돌아가서"}, "toEditYourEmailAddress": {"message": "이메일 주소를 수정하기"}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "접근이 거부되었습니다. 이 페이지를 볼 권한이 없습니다."}, "general": {"message": "일반"}, "display": {"message": "화면"}, "accountSuccessfullyCreated": {"message": "계정이 생성되었습니다!"}, "adminApprovalRequested": {"message": "관리자 승인 필요"}, "adminApprovalRequestSentToAdmins": {"message": "요청이 관리자에게 전송되었습니다."}, "troubleLoggingIn": {"message": "로그인에 문제가 있나요?"}, "loginApproved": {"message": "로그인 승인됨"}, "userEmailMissing": {"message": "사용자 이메일 누락"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "활성화된 사용자의 이메일을 찾을 수 없습니다. 로그아웃합니다."}, "deviceTrusted": {"message": "신뢰할 수 있는 장치"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "입력이 필요합니다."}, "required": {"message": "필수"}, "search": {"message": "검색"}, "inputMinLength": {"message": "입력은 최소한 $COUNT$자 이상이어야 합니다.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "입력 길이는 $COUNT$자를 초과해서는 안 됩니다.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "다음 문자는 허용되지 않습니다: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "입력 값은 최소 $MIN$자 이상이어야 합니다.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "입력 값은 $MAX$ 자를 초과해서는 안 됩니다.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "하나 이상의 이메일이 유효하지 않습니다."}, "inputTrimValidator": {"message": "입력에는 공백만 포함해서는 안 됩니다.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "입력이 이메일 주소가 아닙니다"}, "fieldsNeedAttention": {"message": "위의 $COUNT$ 필드에 주의가 필요합니다", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1개의 필드가 주의가 필요합니다."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ 개의 필드가 주의가 필요합니다.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- 선택 --"}, "multiSelectPlaceholder": {"message": "- 필터링할 유형 --"}, "multiSelectLoading": {"message": "옵션을 검색하는 중..."}, "multiSelectNotFound": {"message": "항목을 찾을 수 없습니다"}, "multiSelectClearAll": {"message": "모두 지우기"}, "plusNMore": {"message": "+ $QUANTITY$개 더보기", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "하위 메뉴"}, "toggleCollapse": {"message": "토글이 붕괴됨", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "도메인 별칭"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "마스터 비밀번호 재 요청이 있는 항목은 페이지 로드에서 자동으로 채울 수 없습니다. 페이지 로드의 자동 완성이 꺼졌습니다.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "페이지 로드 시 자동 완성이 기본 설정을 사용하도록 설정되었습니다.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "마스터 암호 재 요청을 해제하여 이 필드를 편집합니다", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "사이드 내비게이션 전환"}, "skipToContent": {"message": "콘텐츠로 건너뛰기"}, "bitwardenOverlayButton": {"message": "Bitwarden 자동 완성 메뉴 버튼", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Bitwarden 자동 완성메뉴 전환", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden 자동 완성 매뉴", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "일치하는 로그인을 보기위해 계정을 잠금해제하세요", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "계정 잠금을 해제하여 자동 채우기 제안 보기", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "계정 잠금 해제", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "계정 잠금을 해제하기, 새 창에서 열립니다", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "TOTP 인증 코드", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "TOTP 만료까지 남은 시간", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "자격 증명 채우기", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "부분적인 사용자 이름", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "표시할 항목 없음", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "새 항목", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "새 보관함 항목 추가", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "새 로그인", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "새 보관함 로그인 항목 추가, 새 창에서 열립니다", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "새 카드", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "새 보관함 카드 항목 추가, 새 창에서 열립니다", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "신규 ID", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "새 보관함 ID 항목 추가, 새 창에서 열립니다", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden 자동 완성 메뉴를 사용할 수 있습니다. 아래쪽 화살표 키를 눌러 선택하세요.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "켜기"}, "ignore": {"message": "무시하기"}, "importData": {"message": "데이터 가져오기", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "가져오기 오류"}, "importErrorDesc": {"message": "가져오려고 하는 데이터에 문제가 있습니다. 아래에 표시된 파일의 오류를 해결한 뒤 다시 시도해 주세요."}, "resolveTheErrorsBelowAndTryAgain": {"message": "아래 오류를 해결하고 다시 시도하세요."}, "description": {"message": "설명"}, "importSuccess": {"message": "데이터 가져오기 성공"}, "importSuccessNumberOfItems": {"message": "총 $AMOUNT$개의 항목을 가져왔습니다.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "다시 시도"}, "verificationRequiredForActionSetPinToContinue": {"message": "이 작업을 수행하려면 증명이 필요합니다. 계속하려면 PIN을 설정하세요."}, "setPin": {"message": "PIN 설정하기"}, "verifyWithBiometrics": {"message": "생체 인식을 사용하여 증명하기"}, "awaitingConfirmation": {"message": "확인 대기 중"}, "couldNotCompleteBiometrics": {"message": "생체 인식을 완료할 수 없습니다."}, "needADifferentMethod": {"message": "다른 방법이 필요하신가요?"}, "useMasterPassword": {"message": "마스터 비밀번호를 사용하기"}, "usePin": {"message": "PIN 사용하기"}, "useBiometrics": {"message": "생체 인식 사용하기"}, "enterVerificationCodeSentToEmail": {"message": "이메일로 전송된 인증 코드를 입력해주세요"}, "resendCode": {"message": "코드 재전송"}, "total": {"message": "합계"}, "importWarning": {"message": "데이터를 $ORGANIZATION$로 가져오고 있습니다. 데이터를 이 조직의 구성원들과 공유할 수 있습니다. 계속 진행하시겠습니까?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Duo 서비스 연결 중 오류가 발생했습니다. 다른 2단계 로그인 방법을 사용하거나 Duo에 문의하여 도움을 받으세요."}, "duoRequiredForAccount": {"message": "계정에 Duo 2단계 로그인이 필요합니다."}, "popoutExtension": {"message": "확장 프로그램을 새 창에서 열기"}, "launchDuo": {"message": "Duo 실행"}, "importFormatError": {"message": "데이터의 포맷이 올바르지 않습니다. 불러올 파일을 확인하고 다시 시도해 주십시오."}, "importNothingError": {"message": "아무것도 가져오지 못했습니다."}, "importEncKeyError": {"message": "내보내려는 파일을 복호화하던 중 오류가 발생했습니다. 암호화 키가 내보내려는 데이터를 암호화한 키와 일치하지 않습니다."}, "invalidFilePassword": {"message": "파일 비밀번호가 잘못되었습니다. 내보내기 파일을 만들 때 입력한 비밀번호를 사용해 주세요."}, "destination": {"message": "수신자"}, "learnAboutImportOptions": {"message": "가져오기 옵션 알아보기"}, "selectImportFolder": {"message": "폴더 선택"}, "selectImportCollection": {"message": "컬렉션 선택"}, "importTargetHint": {"message": "가져온 파일의 내용을 $DESTINATION$로 이동하려면 이 옵션을 선택하세요.", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "파일에 할당되지 않은 항목이 포함되어 있습니다."}, "selectFormat": {"message": "불러올 파일의 포맷 선택"}, "selectImportFile": {"message": "불러올 파일 선택"}, "chooseFile": {"message": "파일 선택"}, "noFileChosen": {"message": "선택된 파일 없음"}, "orCopyPasteFileContents": {"message": "또는 가져온 파일 내용 복사/붙여넣기"}, "instructionsFor": {"message": "$NAME$ 지침", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "보관함 가져오기 확인"}, "confirmVaultImportDesc": {"message": "이 파일은 비밀번호로 보호받고 있습니다. 데이터를 가져오려면 파일 비밀번호를 입력하세요."}, "confirmFilePassword": {"message": "파일 비밀번호 확인"}, "exportSuccess": {"message": "보관함 데이터 내보내짐"}, "typePasskey": {"message": "패스키"}, "accessing": {"message": "접근 중"}, "loggedInExclamation": {"message": "로그인 완료!"}, "passkeyNotCopied": {"message": "패스키가 복사되지 않습니다"}, "passkeyNotCopiedAlert": {"message": "패스키는 복제된 아이템에 복사되지 않습니다. 계속 이 항목을 복제하시겠어요?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "사이트에서 인증을 요구합니다. 이 기능은 비밀번호가 없는 계정에서는 아직 지원하지 않습니다."}, "logInWithPasskeyQuestion": {"message": "패스키로 로그인하시겠어요?"}, "passkeyAlreadyExists": {"message": "이미 이 애플리케이션에 해당하는 패스키가 있습니다."}, "noPasskeysFoundForThisApplication": {"message": "이 애플리케이션에 대한 패스키를 찾을 수 없습니다."}, "noMatchingPasskeyLogin": {"message": "이 사이트와 일치하는 로그인이 없습니다."}, "noMatchingLoginsForSite": {"message": "사이트와 일치하는 로그인 없음"}, "searchSavePasskeyNewLogin": {"message": "패스키를 새 로그인으로 검색 또는 저장"}, "confirm": {"message": "확인"}, "savePasskey": {"message": "패스키 저장"}, "savePasskeyNewLogin": {"message": "새 로그인으로 패스키 저장"}, "chooseCipherForPasskeySave": {"message": "패스키를 저장할 로그인 선택하기"}, "chooseCipherForPasskeyAuth": {"message": "로그인할 패스키 선택"}, "passkeyItem": {"message": "패스키 항목"}, "overwritePasskey": {"message": "비밀번호를 덮어쓰시겠어요?"}, "overwritePasskeyAlert": {"message": "이 항목은 이미 패스키가 있습니다. 정말로 현재 패스키를 덮어쓰시겠어요?"}, "featureNotSupported": {"message": "아직 지원되지 않는 기능"}, "yourPasskeyIsLocked": {"message": "패스키를 사용하려면 인증이 필요합니다. 인증을 진행해주세요."}, "multifactorAuthenticationCancelled": {"message": "멀티팩터 인증이 취소되었습니다"}, "noLastPassDataFound": {"message": "LastPass 데이터를 찾을 수 없습니다"}, "incorrectUsernameOrPassword": {"message": "잘못된 사용자 이름 또는 비밀번호 입니다."}, "incorrectPassword": {"message": "잘못된 비밀번호입니다"}, "incorrectCode": {"message": "잘못된 코드입니다."}, "incorrectPin": {"message": "올바르지 않은 PIN입니다."}, "multifactorAuthenticationFailed": {"message": "멀티팩터 인증 실패"}, "includeSharedFolders": {"message": "공유 폴더 포함"}, "lastPassEmail": {"message": "LastPass 이메일"}, "importingYourAccount": {"message": "계정 가져오기 중..."}, "lastPassMFARequired": {"message": "LastPass 멀티팩터 인증 필요"}, "lastPassMFADesc": {"message": "인증 앱에서 일회용 비밀번호 입력하기"}, "lastPassOOBDesc": {"message": "인증 앱에서 로그인 요청을 승인하거나 일회용 비밀번호를 입력하세요"}, "passcode": {"message": "비밀번호"}, "lastPassMasterPassword": {"message": "LastPass 마스터 비밀번호"}, "lastPassAuthRequired": {"message": "LastPass 인증 필요"}, "awaitingSSO": {"message": "SSO 인증 대기 중"}, "awaitingSSODesc": {"message": "회사 자격 증명을 사용하여 계속 로그인해 주세요."}, "seeDetailedInstructions": {"message": "도움말 사이트에서 자세한 지침을 확인하세요", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "LastPass에서 직접 가져오기"}, "importFromCSV": {"message": "CSV에서 가져오기"}, "lastPassTryAgainCheckEmail": {"message": "다시 시도하거나 LastPass에서 이메일을 찾아 사용자임을 증명하세요."}, "collection": {"message": "컬렉션"}, "lastPassYubikeyDesc": {"message": "LastPass 계정과 연결된 YubiKey를 컴퓨터의 USB 포트에 삽입한 다음 버튼을 누릅니다."}, "switchAccount": {"message": "계정 전환"}, "switchAccounts": {"message": "계정 전환"}, "switchToAccount": {"message": "계정 전환"}, "activeAccount": {"message": "계정 활성화"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "사용 가능한 계정"}, "accountLimitReached": {"message": "계정 개수 제한에 도달했습니다. 추가로 로그인하려면 다른 계정을 로그아웃 해주세요."}, "active": {"message": "활성"}, "locked": {"message": "잠김"}, "unlocked": {"message": "잠금 해제됨"}, "server": {"message": "서버"}, "hostedAt": {"message": "호스팅된"}, "useDeviceOrHardwareKey": {"message": "기기또는 하드웨어 키를 사용하세요"}, "justOnce": {"message": "한 번만 알림"}, "alwaysForThisSite": {"message": "항상 이 사이트에 대해"}, "domainAddedToExcludedDomains": {"message": "제외된 도메인에 $DOMAIN$이 추가되었습니다.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "일반적인 형식", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "브라우저 설정으로 이동하시겠습니까?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "도움말 센터로 이동하시겠습니까?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "브라우저의 자동 완성 및 비밀번호 관리 설정을 변경합니다.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "브라우저 설정에서 확장 단축키를 보고, 설정할 수 있습니다.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "브라우저의 자동 채우기 및 비밀번호 관리 설정을 변경합니다.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "브라우저 설정에서 확장 단축키를 보고, 설정할 수 있습니다.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Bitwarden을 기본 비밀번호 관리자로 지정하시겠습니까?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "이 옵션을 무시하면 Bitwarden 자동 완성 제안과 브라우저 간에 충돌이 발생할 수 있습니다", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Bitwarden을 기본 비밀번호 관리자로 지정", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Bitwarden을 기본 비밀번호 관리자로 설정할 수 없습니다", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "기본 비밀번호 관리자로 설정하려면 Bitwarden에게 브라우저 개인정보 보호 권한을 부여해야 합니다.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "기본값으로 만들기", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "자격 증명이 성공적으로 저장됨!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "비밀번호 저장됨!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "자격 증명이 성공적으로 업데이트됨!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "비밀번호 업데이트됨!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "자격 증명 저장 중 오류가 발생했습니다. 자세한 내용은 콘솔을 확인하세요.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "성공"}, "removePasskey": {"message": "패스키 제거"}, "passkeyRemoved": {"message": "패스키 제거됨"}, "autofillSuggestions": {"message": "자동 완성 제안"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "이 사이트에서 자동으로 작성할 로그인 항목 저장"}, "yourVaultIsEmpty": {"message": "당신의 보관함이 비어있습니다"}, "noItemsMatchSearch": {"message": "사이트와 일치하는 항목 없음"}, "clearFiltersOrTryAnother": {"message": "필터 지우기 또는 다른 검색어 시도"}, "copyInfoTitle": {"message": "정보 복사 - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "메모 복사 - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "$ITEMNAME$ 의 다른 옵션", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "다른 옵션 - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "항목 보기 - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "자동 완성 - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "복사할 값이 없습니다"}, "assignToCollections": {"message": "컬렉션에 할당하기"}, "copyEmail": {"message": "이메일 복사하기"}, "copyPhone": {"message": "전화번호 복사하기"}, "copyAddress": {"message": "주소 복사하기"}, "adminConsole": {"message": "관리자 콘솔"}, "accountSecurity": {"message": "계정 보안"}, "notifications": {"message": "알림"}, "appearance": {"message": "화면 스타일"}, "errorAssigningTargetCollection": {"message": "대상 컬렉션을 할당하는 중 오류가 발생했습니다."}, "errorAssigningTargetFolder": {"message": "대상 폴더를 할당하는 중 오류가 발생했습니다."}, "viewItemsIn": {"message": "$NAME$에서 항목 보기", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "다시 $NAME$로 돌아가기", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "새 항목"}, "removeItem": {"message": "$NAME$ 제거", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "폴더가 없는 항목"}, "itemDetails": {"message": "항목 세부사항"}, "itemName": {"message": "항목 이름"}, "organizationIsDeactivated": {"message": "조직이 비활성화되었습니다"}, "owner": {"message": "소유자"}, "selfOwnershipLabel": {"message": "당신", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "비활성화된 조직의 항목에 액세스할 수 없습니다. 조직 소유자에게 도움을 요청하세요."}, "additionalInformation": {"message": "추가 정보"}, "itemHistory": {"message": "항목 기록"}, "lastEdited": {"message": "최근 수정 날짜:"}, "ownerYou": {"message": "소유자: 당신"}, "linked": {"message": "연결됨"}, "copySuccessful": {"message": "복사 성공"}, "upload": {"message": "업로드"}, "addAttachment": {"message": "첨부파일 추가"}, "maxFileSizeSansPunctuation": {"message": "최대 파일 크기는 500MB입니다."}, "deleteAttachmentName": {"message": "첨부파일 $NAME$ 삭제", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "$NAME$ 다운로드", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "정말로 이 첨부파일을 영구적으로 삭제하시겠습니까?"}, "premium": {"message": "프리미엄"}, "freeOrgsCannotUseAttachments": {"message": "무료 조직에서는 첨부 파일을 사용할 수 없습니다."}, "filters": {"message": "필터"}, "filterVault": {"message": "보관함 필터링"}, "filterApplied": {"message": "필터 1개가 적용되었습니다"}, "filterAppliedPlural": {"message": "$COUNT$개의 필터가 적용되었습니다", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "개인 정보"}, "identification": {"message": "본인 확인"}, "contactInfo": {"message": "연락처 정보"}, "downloadAttachment": {"message": "다운로드 - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "카드 번호는 다음으로 끝납니다", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "로그인 정보"}, "authenticatorKey": {"message": "인증 키"}, "autofillOptions": {"message": "자동 완성 옵션"}, "websiteUri": {"message": "웹사이트 (URI)"}, "websiteUriCount": {"message": "웹사이트 (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "웹사이트 추가됨"}, "addWebsite": {"message": "웹사이트 추가"}, "deleteWebsite": {"message": "웹사이트 삭제"}, "defaultLabel": {"message": "기본값 ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "$WEBSITE$ 일치 인식 보이기", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "$WEBSITE$ 일치 인식 숨기기", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "페이지 로드 시 자동 완성을 할까요?"}, "cardExpiredTitle": {"message": "만료된 카드"}, "cardExpiredMessage": {"message": "갱신한 경우, 카드 정보를 업데이트합니다"}, "cardDetails": {"message": "카드 상세정보"}, "cardBrandDetails": {"message": "$BRAND$ 상세정보", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "애니메이션 활성화"}, "showAnimations": {"message": "애니메이션 표시"}, "addAccount": {"message": "계정 추가"}, "loading": {"message": "불러오는 중"}, "data": {"message": "데이터"}, "passkeys": {"message": "패스키", "description": "A section header for a list of passkeys."}, "passwords": {"message": "비밀번호", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "패스키로 로그인", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "할당"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "이 컬렉션에 액세스할 수 있는 조직 구성원만 해당 항목을 볼 수 있습니다."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "이 컬렉션에 액세스할 수 있는 조직 구성원만 해당 항목들을 볼 수 있습니다."}, "bulkCollectionAssignmentWarning": {"message": "$TOTAL_COUNT$ 항목들을 선택했습니다. 편집 권한이 없기 때문에 항목들의 $READONLY_COUNT$를 업데이트할 수 없습니다.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "필드 추가"}, "add": {"message": "추가"}, "fieldType": {"message": "필드 유형"}, "fieldLabel": {"message": "필드 레이블"}, "textHelpText": {"message": "보안 질문과 같은 데이터에 텍스트 필드를 사용하세요"}, "hiddenHelpText": {"message": "비밀번호와 같은 중요한 데이터의 경우 숨겨진 필드를 사용하세요."}, "checkBoxHelpText": {"message": "이메일 기억과 같이 양식의 체크박스를 자동으로 채우려면 체크박스들을 사용하세요"}, "linkedHelpText": {"message": "특정 웹사이트에 대한 자동 채우기 문제가 발생할 때는, 연결 필드를 사용하세요"}, "linkedLabelHelpText": {"message": "필드의 html ID, 이름, aria-label 또는 플레이스홀더를 입력하세요"}, "editField": {"message": "필드 편집"}, "editFieldLabel": {"message": "$LABEL$ 편집", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "$LABEL$ 삭제", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ 추가됨", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "$LABEL$을 재정렬합니다. 화살표 키를 사용하여 항목을 위나 아래로 이동할 수 있습니다.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$을 위로 이동했습니다. 위치: $INDEX$ / $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "할당할 컬렉션을 선택하세요"}, "personalItemTransferWarningSingular": {"message": "1개 항목이 선택한 조직으로 영구적으로 전송됩니다. 더 이상 이 항목을 소유하지 않습니다."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ 개 항목들이 선택한 조직으로 영구적으로 전송됩니다. 더 이상 이 항목들을 소유하지 않습니다.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1개 항목이 $ORG$으로 영구적으로 전송됩니다. 더 이상 이 항목을 소유하지 않습니다.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ 개 항목들이 $ORG$으로 영구적으로 전송됩니다. 더 이상 이 항목들을 소유하지 않습니다.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "성공적으로 컬렉션을 할당했습니다"}, "nothingSelected": {"message": "아무것도 선택하지 않았습니다."}, "itemsMovedToOrg": {"message": "항목들이 $ORGNAME$로 이동했습니다", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "항목이 $ORGNAME$로 이동했습니다", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$을 아래로 이동했습니다. 위치: $INDEX$ / $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "항목 위치"}, "fileSend": {"message": "파일 Send"}, "fileSends": {"message": "파일 Send"}, "textSend": {"message": "텍스트 Send"}, "textSends": {"message": "텍스트 Send"}, "accountActions": {"message": "계정 작업"}, "showNumberOfAutofillSuggestions": {"message": "확장 아이콘에 로그인 자동 완성 제안 수 표시"}, "showQuickCopyActions": {"message": "보관함에서 빠른 복사 기능 표시"}, "systemDefault": {"message": "시스템 기본 설정"}, "enterprisePolicyRequirementsApplied": {"message": "기업 정책에 따른 요구사항들이 옵션들에 적용되었습니다."}, "sshPrivateKey": {"message": "개인 키"}, "sshPublicKey": {"message": "공개 키"}, "sshFingerprint": {"message": "지문"}, "sshKeyAlgorithm": {"message": "키 유형"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "재시도"}, "vaultCustomTimeoutMinimum": {"message": "최소 사용자 지정 시간 초과는 1분입니다."}, "additionalContentAvailable": {"message": "추가 콘텐츠를 사용할 수 있습니다"}, "fileSavedToDevice": {"message": "파일을 장치에 저장했습니다. 장치 다운로드로 관리할 수 있습니다."}, "showCharacterCount": {"message": "글자 수 표시하기"}, "hideCharacterCount": {"message": "글자 수 숨기기"}, "itemsInTrash": {"message": "휴지통에 있는 항목"}, "noItemsInTrash": {"message": "휴지통에 항목이 없습니다."}, "noItemsInTrashDesc": {"message": "삭제한 항목은 여기에 표시되며 30일 후 영구적으로 삭제됩니다."}, "trashWarning": {"message": "30일 이상 휴지통에 보관된 항목은 자동으로 삭제됩니다."}, "restore": {"message": "복원"}, "deleteForever": {"message": "영구 삭제하기"}, "noEditPermissions": {"message": "아이템을 수정할 권한이 없습니다."}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "인증 중"}, "fillGeneratedPassword": {"message": "생성된 비밀번호를 입력하세요", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "비밀번호가 재생성되었습니다.", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "스페이스", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "물결표(~)", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "백틱(`)", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "느낌표 (!)", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "골뱅이표 (@)", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "해시 기호 (#)", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "달러 기호 ($)", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "퍼센트 기호 (%)", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "캐럿 기호 (^)", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "앰퍼샌드 기호 (&)", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "별표 (*)", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "왼쪽 소괄호 ' ( '", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "오른쪽 소괄호 ' ) '", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "밑줄( _ )", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "붙임표 ( - )", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "더하기 기호 ( + )", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "등호 ( = )", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "왼쪽 중괄호 ' { '", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "오른쪽 중괄호 ' } '", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "왼쪽 대괄호 ' [ '", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "오른쪽 대괄호 ' ] '", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "파이프 기호 ( | )", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "백슬래시 ( \\ )", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "콜론 ( : )", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "세미콜론( ; )", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "쌍 따옴표 ( \" )", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "홑 따옴표 ( ' )", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "보다 작음 ( < )", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "보다 큰 ( > )", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "쉼표( , )", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "마침표 ( . )", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "물음표 ( ? )", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "슬래시 ( / )", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "소문자"}, "uppercaseAriaLabel": {"message": "대문자"}, "generatedPassword": {"message": "비밀번호 생성"}, "compactMode": {"message": "컴팩트 모드\n"}, "beta": {"message": "베타"}, "extensionWidth": {"message": "확장 폭"}, "wide": {"message": "넓게"}, "extraWide": {"message": "매우 넓게"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "보기 권한만 있는 컬렉션은 제거할 수 없습니다: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}