@import "variables.scss";

.box {
  position: relative;
  width: 100%;

  &.first {
    margin-top: 0;
  }

  .box-header {
    margin: 0 10px 5px 10px;
    text-transform: uppercase;
    display: flex;

    @include themify($themes) {
      color: themed("headingColor");
    }
  }

  .box-content {
    @include themify($themes) {
      background-color: themed("backgroundColor");
      border-color: themed("borderColor");
    }

    &.box-content-padded {
      padding: 10px 15px;
    }

    &.condensed .box-content-row,
    .box-content-row.condensed {
      padding-top: 5px;
      padding-bottom: 5px;
    }

    &.no-hover .box-content-row,
    .box-content-row.no-hover {
      &:hover,
      &:focus {
        @include themify($themes) {
          background-color: themed("boxBackgroundColor") !important;
        }
      }
    }

    &.single-line .box-content-row,
    .box-content-row.single-line {
      padding-top: 10px;
      padding-bottom: 10px;
      margin: 5px;
    }

    &.row-top-padding {
      padding-top: 10px;
    }
  }

  .box-footer {
    margin: 0 5px 5px 5px;
    padding: 0 10px 5px 10px;
    font-size: $font-size-small;

    button.btn {
      font-size: $font-size-small;
      padding: 0;
    }

    button.btn.primary {
      font-size: $font-size-base;
      padding: 7px 15px;
      width: 100%;

      &:hover {
        @include themify($themes) {
          border-color: themed("borderHoverColor") !important;
        }
      }
    }

    @include themify($themes) {
      color: themed("mutedColor");
    }
  }

  &.list {
    margin: 10px 0 20px 0;
    .box-content {
      .virtual-scroll-item {
        display: inline-block;
        width: 100%;
      }

      .box-content-row {
        text-decoration: none;
        border-radius: $border-radius;
        // background-color: $background-color;

        @include themify($themes) {
          color: themed("textColor");
          background-color: themed("boxBackgroundColor");
        }

        &.padded {
          padding-top: 10px;
          padding-bottom: 10px;
        }

        &.no-hover {
          &:hover {
            @include themify($themes) {
              background-color: themed("boxBackgroundColor") !important;
            }
          }
        }

        &:hover,
        &:focus,
        &.active {
          @include themify($themes) {
            background-color: themed("listItemBackgroundHoverColor");
          }
        }

        &:focus {
          border-left: 5px solid #000000;
          padding-left: 5px;

          @include themify($themes) {
            border-left-color: themed("mutedColor");
          }
        }

        .action-buttons {
          .row-btn {
            padding-left: 5px;
            padding-right: 5px;
          }
        }

        .text:not(.no-ellipsis),
        .detail {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .row-main {
          display: flex;
          min-width: 0;
          align-items: normal;

          .row-main-content {
            min-width: 0;
          }
        }
      }

      &.single-line {
        .box-content-row {
          display: flex;
          padding-top: 10px;
          padding-bottom: 10px;
          margin: 5px;
          border-radius: $border-radius;
        }
      }
    }
  }
}

.box-content-row {
  display: block;
  padding: 5px 10px;
  position: relative;
  z-index: 1;
  border-radius: $border-radius;
  margin: 3px 5px;

  @include themify($themes) {
    background-color: themed("boxBackgroundColor");
  }

  &:last-child {
    &:before {
      border: none;
      height: 0;
    }
  }

  &.override-last:last-child:before {
    border-bottom: 1px solid #000000;
    @include themify($themes) {
      border-bottom-color: themed("boxBorderColor");
    }
  }

  &.last:last-child:before {
    border-bottom: 1px solid #000000;
    @include themify($themes) {
      border-bottom-color: themed("boxBorderColor");
    }
  }

  &:after {
    content: "";
    display: table;
    clear: both;
  }

  &:hover,
  &:focus,
  &.active {
    @include themify($themes) {
      background-color: themed("boxBackgroundHoverColor");
    }
  }

  &.pre {
    white-space: pre;
    overflow-x: auto;
  }

  &.pre-wrap {
    white-space: pre-wrap;
    overflow-x: auto;
  }

  .row-label,
  label {
    font-size: $font-size-small;
    display: block;
    width: 100%;
    margin-bottom: 5px;

    @include themify($themes) {
      color: themed("mutedColor");
    }

    .sub-label {
      margin-left: 10px;
    }
  }

  .flex-label {
    font-size: $font-size-small;
    display: flex;
    flex-grow: 1;
    margin-bottom: 5px;

    @include themify($themes) {
      color: themed("mutedColor");
    }

    > a {
      flex-grow: 0;
    }
  }

  .text,
  .detail {
    display: block;
    text-align: left;

    @include themify($themes) {
      color: themed("textColor");
    }
  }

  .detail {
    font-size: $font-size-small;

    @include themify($themes) {
      color: themed("mutedColor");
    }
  }

  .img-right,
  .txt-right {
    float: right;
    margin-left: 10px;
  }

  .row-main {
    flex-grow: 1;
    min-width: 0;
  }

  &.box-content-row-flex,
  .box-content-row-flex,
  &.box-content-row-checkbox,
  &.box-content-row-link,
  &.box-content-row-input,
  &.box-content-row-slider,
  &.box-content-row-multi {
    display: flex;
    align-items: center;
    word-break: break-all;

    &.box-content-row-word-break {
      word-break: normal;
    }
  }

  &.box-content-row-multi {
    input:not([type="checkbox"]) {
      width: 100%;
    }

    input + label.sr-only + select {
      margin-top: 5px;
    }

    > a,
    > button {
      padding: 8px 8px 8px 4px;
      margin: 0;

      @include themify($themes) {
        color: themed("dangerColor");
      }
    }
  }

  &.box-content-row-multi,
  &.box-content-row-newmulti {
    padding-left: 10px;
  }

  &.box-content-row-newmulti {
    @include themify($themes) {
      color: themed("primaryColor");
    }
  }

  &.box-content-row-checkbox,
  &.box-content-row-link,
  &.box-content-row-input,
  &.box-content-row-slider {
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 5px;

    label,
    .row-label {
      font-size: $font-size-base;
      display: block;
      width: initial;
      margin-bottom: 0;

      @include themify($themes) {
        color: themed("textColor");
      }
    }

    > span {
      @include themify($themes) {
        color: themed("mutedColor");
      }
    }

    > input {
      margin: 0 0 0 auto;
      padding: 0;
    }

    > * {
      margin-right: 15px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &.box-content-row-checkbox-left {
    justify-content: flex-start;

    > input {
      margin: 0 15px 0 0;
    }
  }

  &.box-content-row-input {
    label {
      white-space: nowrap;
    }

    input {
      text-align: right;

      &[type="number"] {
        max-width: 50px;
      }
    }
  }

  &.box-content-row-slider {
    input[type="range"] {
      height: 10px;
    }

    input[type="number"] {
      width: 45px;
    }

    label {
      white-space: nowrap;
    }
  }

  input:not([type="checkbox"]):not([type="radio"]),
  textarea {
    border: none;
    width: 100%;
    background-color: transparent !important;

    &::-webkit-input-placeholder {
      @include themify($themes) {
        color: themed("inputPlaceholderColor");
      }
    }

    &:not([type="file"]):focus {
      outline: none;
    }
  }

  select {
    width: 100%;
    border: 1px solid #000000;
    border-radius: $border-radius;
    padding: 7px 4px;

    @include themify($themes) {
      border-color: themed("inputBorderColor");
    }
  }

  .action-buttons {
    display: flex;
    margin-left: 5px;

    &.action-buttons-fixed {
      align-self: start;
      margin-top: 2px;
    }

    .row-btn {
      cursor: pointer;
      padding: 10px 8px;
      background: none;
      border: none;

      @include themify($themes) {
        color: themed("boxRowButtonColor");
      }

      &:hover,
      &:focus {
        @include themify($themes) {
          color: themed("boxRowButtonHoverColor");
        }
      }

      &.disabled,
      &[disabled] {
        @include themify($themes) {
          color: themed("disabledIconColor");
          opacity: themed("disabledBoxOpacity");
        }

        &:hover {
          @include themify($themes) {
            color: themed("disabledIconColor");
            opacity: themed("disabledBoxOpacity");
          }
        }
        cursor: default !important;
      }
    }

    &.no-pad .row-btn {
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  &:not(.box-draggable-row) {
    .action-buttons .row-btn:last-child {
      margin-right: -3px;
    }
  }

  &.box-draggable-row {
    &.box-content-row-checkbox {
      input[type="checkbox"] + .drag-handle {
        margin-left: 10px;
      }
    }
  }

  .drag-handle {
    cursor: move;
    padding: 10px 2px 10px 8px;
    user-select: none;

    @include themify($themes) {
      color: themed("mutedColor");
    }
  }

  &.cdk-drag-preview {
    position: relative;
    display: flex;
    align-items: center;
    opacity: 0.8;

    @include themify($themes) {
      background-color: themed("boxBackgroundColor");
    }
  }

  select.field-type {
    margin: 5px 0 0 25px;
    width: calc(100% - 25px);
  }

  .row-sub-icon,
  .row-sub-label + i.bwi {
    @include themify($themes) {
      color: themed("disabledIconColor");
    }
  }

  .icon {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 34px;
    margin-left: -5px;

    @include themify($themes) {
      color: themed("mutedColor");
    }

    &.icon-small {
      min-width: 25px;
    }

    img {
      border-radius: $border-radius;
      max-height: 20px;
      max-width: 20px;
    }
  }

  .progress {
    display: flex;
    height: 5px;
    overflow: hidden;
    margin: 5px -15px -10px;

    .progress-bar {
      display: flex;
      flex-direction: column;
      justify-content: center;
      white-space: nowrap;
      background-color: $brand-primary;
    }
  }

  .radio-group {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;

    input {
      flex-grow: 0;
    }

    label {
      margin: 0 0 0 5px;
      flex-grow: 1;
      font-size: $font-size-base;
      display: block;
      width: 100%;

      @include themify($themes) {
        color: themed("textColor");
      }
    }

    &.align-start {
      align-items: start;
      margin-top: 10px;

      label {
        margin-top: -4px;
      }
    }
  }
}

.truncate {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

form {
  .box {
    .box-content {
      .box-content-row {
        &.no-hover {
          &:hover {
            @include themify($themes) {
              background-color: themed("transparentColor") !important;
            }
          }
        }
      }
    }
  }
}
