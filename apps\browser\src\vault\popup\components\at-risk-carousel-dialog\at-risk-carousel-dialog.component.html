<bit-simple-dialog hideIcon>
  <div bitDialogContent>
    <vault-carousel label="Placeholder" (slideChange)="onSlideChange($event)">
      <vault-carousel-slide [label]="'reviewAtRiskLogins' | i18n">
        <img
          class="tw-max-w-full tw-max-h-40"
          src="../../../../images/at-risk-password-carousel/review_at-risk_logins.light.png"
          appDarkImgSrc="../../../../images/at-risk-password-carousel/review_at-risk_logins.dark.png"
          [alt]="'reviewAtRiskLoginSlideImgAltPeriod' | i18n"
        />
        <h2 bitTypography="h2" class="tw-mt-8">{{ "reviewAtRiskLogins" | i18n }}</h2>
        <p bitTypography="body1">
          {{ "reviewAtRiskLoginsSlideDesc" | i18n }}
        </p>
      </vault-carousel-slide>
      <vault-carousel-slide [label]="'generatePassword' | i18n">
        <img
          class="tw-max-w-full tw-max-h-40"
          src="../../../../images/at-risk-password-carousel/generate_password.light.png"
          appDarkImgSrc="../../../../images/at-risk-password-carousel/generate_password.dark.png"
          [alt]="'generatePasswordSlideImgAltPeriod' | i18n"
        />
        <h2 bitTypography="h2" class="tw-mt-8">{{ "generatePassword" | i18n }}</h2>
        <p bitTypography="body1">
          {{ "generatePasswordSlideDesc" | i18n }}
        </p>
      </vault-carousel-slide>
      <vault-carousel-slide [label]="'updateInBitwarden' | i18n">
        <img
          class="tw-max-w-full tw-max-h-40"
          src="../../../../images/at-risk-password-carousel/update_login.light.png"
          appDarkImgSrc="../../../../images/at-risk-password-carousel/update_login.dark.png"
          [alt]="'updateInBitwardenSlideImgAltPeriod' | i18n"
        />
        <h2 bitTypography="h2" class="tw-mt-8">{{ "updateInBitwarden" | i18n }}</h2>
        <p bitTypography="body1">
          {{ "updateInBitwardenSlideDesc" | i18n }}
        </p>
      </vault-carousel-slide>
    </vault-carousel>
  </div>
  <ng-container bitDialogFooter>
    <button
      type="button"
      bitButton
      buttonType="primary"
      block
      [disabled]="!dismissBtnEnabled()"
      (click)="dismiss()"
      data-testid="confirm-carousel-button"
    >
      {{ "reviewAtRiskPasswords" | i18n }}
    </button>
  </ng-container>
</bit-simple-dialog>
