<bit-section *ngIf="ciphers?.length">
  <bit-section-header>
    <h2 bitTypography="h6">
      {{ headerText }}
    </h2>
    <span bitTypography="body1" slot="end">{{ ciphers.length }}</span>
  </bit-section-header>
  <bit-item-group>
    <bit-item *ngFor="let cipher of ciphers">
      <button
        bit-item-content
        type="button"
        [appA11yTitle]="'viewItemTitle' | i18n: cipher.name"
        (click)="onViewCipher(cipher)"
      >
        <div slot="start" class="tw-justify-start tw-w-7 tw-flex">
          <app-vault-icon [cipher]="cipher"></app-vault-icon>
        </div>
        <span data-testid="item-name">{{ cipher.name }}</span>
        <i
          *ngIf="cipher.organizationId"
          appOrgIcon
          [tierType]="cipher.organization.productTierType"
          [size]="'small'"
          [appA11yTitle]="orgIconTooltip(cipher)"
        ></i>
        <i
          *ngIf="cipher.hasAttachments"
          class="bwi bwi-paperclip bwi-sm"
          [appA11yTitle]="'attachments' | i18n"
        ></i>
        <span slot="secondary">{{ cipher.subTitle }}</span>
      </button>
      <ng-container slot="end" *ngIf="cipher.permissions.restore">
        <bit-item-action>
          <button
            type="button"
            bitIconButton="bwi-ellipsis-v"
            size="small"
            [attr.aria-label]="'moreOptionsLabel' | i18n: cipher.name"
            [title]="'moreOptionsTitle' | i18n: cipher.name"
            [bitMenuTriggerFor]="moreOptions"
          ></button>
          <bit-menu #moreOptions>
            <button
              type="button"
              bitMenuItem
              (click)="restore(cipher)"
              *ngIf="!cipher.decryptionFailure"
            >
              {{ "restore" | i18n }}
            </button>
            <button type="button" bitMenuItem *appCanDeleteCipher="cipher" (click)="delete(cipher)">
              {{ "deleteForever" | i18n }}
            </button>
          </bit-menu>
        </bit-item-action>
      </ng-container>
    </bit-item>
  </bit-item-group>
</bit-section>
