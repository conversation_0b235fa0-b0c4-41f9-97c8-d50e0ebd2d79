<bit-item-action>
  <button
    type="button"
    bitIconButton="bwi-ellipsis-v"
    size="small"
    [attr.aria-label]="'moreOptionsLabel' | i18n: cipher.name"
    [title]="'moreOptionsTitle' | i18n: cipher.name"
    [disabled]="cipher.decryptionFailure"
    [bitMenuTriggerFor]="moreOptions"
  ></button>
  <bit-menu #moreOptions>
    <ng-container *ngIf="canAutofill && !hideAutofillOptions">
      <ng-container *ngIf="autofillAllowed$ | async">
        <button type="button" bitMenuItem (click)="doAutofill()">
          {{ "autofill" | i18n }}
        </button>
        <button type="button" bitMenuItem *ngIf="canEdit && isLogin" (click)="doAutofillAndSave()">
          {{ "fillAndSave" | i18n }}
        </button>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="showViewOption">
      <button type="button" bitMenuItem (click)="onView()">
        {{ "view" | i18n }}
      </button>
    </ng-container>
    <button type="button" bitMenuItem (click)="toggleFavorite()">
      {{ favoriteText | i18n }}
    </button>
    <ng-container *ngIf="canEdit && canViewPassword">
      <a bitMenuItem (click)="clone()" *ngIf="canClone$ | async">
        {{ "clone" | i18n }}
      </a>
      <a
        bitMenuItem
        *ngIf="canAssignCollections$ | async"
        (click)="conditionallyNavigateToAssignCollections()"
      >
        {{ "assignToCollections" | i18n }}
      </a>
    </ng-container>
  </bit-menu>
</bit-item-action>
