// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		03100CAF291891F4008E14EF /* encrypt-worker.js in Resources */ = {isa = PBXBuildFile; fileRef = 03100CAE291891F4008E14EF /* encrypt-worker.js */; };
		55BC93932CB4268A008CA4C6 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 55BC93922CB4268A008CA4C6 /* assets */; };
		55E0374D2577FA6B00979016 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55E0374C2577FA6B00979016 /* AppDelegate.swift */; };
		55E037502577FA6B00979016 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 55E0374E2577FA6B00979016 /* Main.storyboard */; };
		55E037522577FA6B00979016 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55E037512577FA6B00979016 /* ViewController.swift */; };
		55E037542577FA6E00979016 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 55E037532577FA6E00979016 /* Assets.xcassets */; };
		55E0375B2577FA6F00979016 /* safari.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 55E0375A2577FA6F00979016 /* safari.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		55E037602577FA6F00979016 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 55E0375F2577FA6F00979016 /* Cocoa.framework */; };
		55E037632577FA6F00979016 /* SafariWebExtensionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55E037622577FA6F00979016 /* SafariWebExtensionHandler.swift */; };
		55E037792577FA6F00979016 /* popup in Resources */ = {isa = PBXBuildFile; fileRef = 55E037702577FA6F00979016 /* popup */; };
		55E0377A2577FA6F00979016 /* background.js in Resources */ = {isa = PBXBuildFile; fileRef = 55E037712577FA6F00979016 /* background.js */; };
		55E0377B2577FA6F00979016 /* images in Resources */ = {isa = PBXBuildFile; fileRef = 55E037722577FA6F00979016 /* images */; };
		55E0377C2577FA6F00979016 /* notification in Resources */ = {isa = PBXBuildFile; fileRef = 55E037732577FA6F00979016 /* notification */; };
		55E0377D2577FA6F00979016 /* content in Resources */ = {isa = PBXBuildFile; fileRef = 55E037742577FA6F00979016 /* content */; };
		55E0377E2577FA6F00979016 /* vendor.js in Resources */ = {isa = PBXBuildFile; fileRef = 55E037752577FA6F00979016 /* vendor.js */; };
		55E0377F2577FA6F00979016 /* manifest.json in Resources */ = {isa = PBXBuildFile; fileRef = 55E037762577FA6F00979016 /* manifest.json */; };
		55E037802577FA6F00979016 /* background.html in Resources */ = {isa = PBXBuildFile; fileRef = 55E037772577FA6F00979016 /* background.html */; };
		55E037812577FA6F00979016 /* _locales in Resources */ = {isa = PBXBuildFile; fileRef = 55E037782577FA6F00979016 /* _locales */; };
		55E037822577FA6F00979016 /* overlay in Resources */ = {isa = PBXBuildFile; fileRef = 55E037832577FA6F00979016 /* overlay */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		55E0375C2577FA6F00979016 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 55E037402577FA6B00979016 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 55E037592577FA6F00979016;
			remoteInfo = safari;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		55E0376B2577FA6F00979016 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				55E0375B2577FA6F00979016 /* safari.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		03100CAE291891F4008E14EF /* encrypt-worker.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; name = "encrypt-worker.js"; path = "../../../build/encrypt-worker.js"; sourceTree = "<group>"; };
		5508DD7926051B5900A85C58 /* libswiftAppKit.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftAppKit.tbd; path = usr/lib/swift/libswiftAppKit.tbd; sourceTree = SDKROOT; };
		55BC93922CB4268A008CA4C6 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; name = assets; path = ../../../build/assets; sourceTree = "<group>"; };
		55E037482577FA6B00979016 /* desktop.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = desktop.app; sourceTree = BUILT_PRODUCTS_DIR; };
		55E0374B2577FA6B00979016 /* desktop.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = desktop.entitlements; sourceTree = "<group>"; };
		55E0374C2577FA6B00979016 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		55E0374F2577FA6B00979016 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		55E037512577FA6B00979016 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		55E037532577FA6E00979016 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		55E037552577FA6E00979016 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		55E0375A2577FA6F00979016 /* safari.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = safari.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		55E0375F2577FA6F00979016 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		55E037622577FA6F00979016 /* SafariWebExtensionHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafariWebExtensionHandler.swift; sourceTree = "<group>"; };
		55E037642577FA6F00979016 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		55E037652577FA6F00979016 /* safari.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = safari.entitlements; sourceTree = "<group>"; };
		55E037702577FA6F00979016 /* popup */ = {isa = PBXFileReference; lastKnownFileType = folder; name = popup; path = ../../../build/popup; sourceTree = "<group>"; };
		55E037712577FA6F00979016 /* background.js */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.javascript; name = background.js; path = ../../../build/background.js; sourceTree = "<group>"; };
		55E037722577FA6F00979016 /* images */ = {isa = PBXFileReference; lastKnownFileType = folder; name = images; path = ../../../build/images; sourceTree = "<group>"; };
		55E037732577FA6F00979016 /* notification */ = {isa = PBXFileReference; lastKnownFileType = folder; name = notification; path = ../../../build/notification; sourceTree = "<group>"; };
		55E037742577FA6F00979016 /* content */ = {isa = PBXFileReference; lastKnownFileType = folder; name = content; path = ../../../build/content; sourceTree = "<group>"; };
		55E037752577FA6F00979016 /* vendor.js */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.javascript; name = vendor.js; path = ../../../build/vendor.js; sourceTree = "<group>"; };
		55E037762577FA6F00979016 /* manifest.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = manifest.json; path = ../../../build/manifest.json; sourceTree = "<group>"; };
		55E037772577FA6F00979016 /* background.html */ = {isa = PBXFileReference; lastKnownFileType = text.html; name = background.html; path = ../../../build/background.html; sourceTree = "<group>"; };
		55E037782577FA6F00979016 /* _locales */ = {isa = PBXFileReference; lastKnownFileType = folder; name = _locales; path = ../../../build/_locales; sourceTree = "<group>"; };
		55E037832577FA6F00979016 /* overlay */ = {isa = PBXFileReference; lastKnownFileType = folder; name = overlay; path = ../../../build/overlay; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		55E037452577FA6B00979016 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		55E037572577FA6F00979016 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55E037602577FA6F00979016 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		55E0373F2577FA6B00979016 = {
			isa = PBXGroup;
			children = (
				55E0374A2577FA6B00979016 /* desktop */,
				55E037612577FA6F00979016 /* safari */,
				55E0375E2577FA6F00979016 /* Frameworks */,
				55E037492577FA6B00979016 /* Products */,
			);
			sourceTree = "<group>";
		};
		55E037492577FA6B00979016 /* Products */ = {
			isa = PBXGroup;
			children = (
				55E037482577FA6B00979016 /* desktop.app */,
				55E0375A2577FA6F00979016 /* safari.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		55E0374A2577FA6B00979016 /* desktop */ = {
			isa = PBXGroup;
			children = (
				55E0374B2577FA6B00979016 /* desktop.entitlements */,
				55E0374C2577FA6B00979016 /* AppDelegate.swift */,
				55E0374E2577FA6B00979016 /* Main.storyboard */,
				55E037512577FA6B00979016 /* ViewController.swift */,
				55E037532577FA6E00979016 /* Assets.xcassets */,
				55E037552577FA6E00979016 /* Info.plist */,
			);
			path = desktop;
			sourceTree = "<group>";
		};
		55E0375E2577FA6F00979016 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5508DD7926051B5900A85C58 /* libswiftAppKit.tbd */,
				55E0375F2577FA6F00979016 /* Cocoa.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		55E037612577FA6F00979016 /* safari */ = {
			isa = PBXGroup;
			children = (
				55E0376F2577FA6F00979016 /* Resources */,
				55E037622577FA6F00979016 /* SafariWebExtensionHandler.swift */,
				55E037642577FA6F00979016 /* Info.plist */,
				55E037652577FA6F00979016 /* safari.entitlements */,
			);
			path = safari;
			sourceTree = "<group>";
		};
		55E0376F2577FA6F00979016 /* Resources */ = {
			isa = PBXGroup;
			children = (
				55BC93922CB4268A008CA4C6 /* assets */,
				03100CAE291891F4008E14EF /* encrypt-worker.js */,
				55E037702577FA6F00979016 /* popup */,
				55E037712577FA6F00979016 /* background.js */,
				55E037722577FA6F00979016 /* images */,
				55E037732577FA6F00979016 /* notification */,
				55E037742577FA6F00979016 /* content */,
				55E037752577FA6F00979016 /* vendor.js */,
				55E037762577FA6F00979016 /* manifest.json */,
				55E037772577FA6F00979016 /* background.html */,
				55E037782577FA6F00979016 /* _locales */,
				55E037832577FA6F00979016 /* overlay */,
			);
			name = Resources;
			path = safari;
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		55E037472577FA6B00979016 /* desktop */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 55E0376C2577FA6F00979016 /* Build configuration list for PBXNativeTarget "desktop" */;
			buildPhases = (
				55E037442577FA6B00979016 /* Sources */,
				55E037452577FA6B00979016 /* Frameworks */,
				55E037462577FA6B00979016 /* Resources */,
				55E0376B2577FA6F00979016 /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				55E0375D2577FA6F00979016 /* PBXTargetDependency */,
			);
			name = desktop;
			productName = desktop;
			productReference = 55E037482577FA6B00979016 /* desktop.app */;
			productType = "com.apple.product-type.application";
		};
		55E037592577FA6F00979016 /* safari */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 55E037682577FA6F00979016 /* Build configuration list for PBXNativeTarget "safari" */;
			buildPhases = (
				55E037562577FA6F00979016 /* Sources */,
				55E037572577FA6F00979016 /* Frameworks */,
				55E037582577FA6F00979016 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = safari;
			productName = safari;
			productReference = 55E0375A2577FA6F00979016 /* safari.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		55E037402577FA6B00979016 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1220;
				LastUpgradeCheck = 1220;
				ORGANIZATIONNAME = "8bit Solutions LLC";
				TargetAttributes = {
					55E037472577FA6B00979016 = {
						CreatedOnToolsVersion = 12.2;
					};
					55E037592577FA6F00979016 = {
						CreatedOnToolsVersion = 12.2;
						SystemCapabilities = {
							com.apple.Sandbox = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 55E037432577FA6B00979016 /* Build configuration list for PBXProject "desktop" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 55E0373F2577FA6B00979016;
			productRefGroup = 55E037492577FA6B00979016 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				55E037472577FA6B00979016 /* desktop */,
				55E037592577FA6F00979016 /* safari */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		55E037462577FA6B00979016 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55E037542577FA6E00979016 /* Assets.xcassets in Resources */,
				55E037502577FA6B00979016 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		55E037582577FA6F00979016 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55E037812577FA6F00979016 /* _locales in Resources */,
				55E0377B2577FA6F00979016 /* images in Resources */,
				55E0377F2577FA6F00979016 /* manifest.json in Resources */,
				55E037802577FA6F00979016 /* background.html in Resources */,
				55E0377A2577FA6F00979016 /* background.js in Resources */,
				55E037792577FA6F00979016 /* popup in Resources */,
				03100CAF291891F4008E14EF /* encrypt-worker.js in Resources */,
				55BC93932CB4268A008CA4C6 /* assets in Resources */,
				55E0377C2577FA6F00979016 /* notification in Resources */,
				55E0377E2577FA6F00979016 /* vendor.js in Resources */,
				55E0377D2577FA6F00979016 /* content in Resources */,
				55E037822577FA6F00979016 /* overlay in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		55E037442577FA6B00979016 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55E037522577FA6B00979016 /* ViewController.swift in Sources */,
				55E0374D2577FA6B00979016 /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		55E037562577FA6F00979016 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55E037632577FA6F00979016 /* SafariWebExtensionHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		55E0375D2577FA6F00979016 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 55E037592577FA6F00979016 /* safari */;
			targetProxy = 55E0375C2577FA6F00979016 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		55E0374E2577FA6B00979016 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				55E0374F2577FA6B00979016 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		55E037662577FA6F00979016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		55E037672577FA6F00979016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		55E037692577FA6F00979016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_ENTITLEMENTS = safari/safari.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = LTZ2PFU5D6;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = safari/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitwarden.desktop.safari;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		55E0376A2577FA6F00979016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_ENTITLEMENTS = safari/safari.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = LTZ2PFU5D6;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = safari/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitwarden.desktop.safari;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		55E0376D2577FA6F00979016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = desktop/desktop.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = LTZ2PFU5D6;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = desktop/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitwarden.desktop;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		55E0376E2577FA6F00979016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = desktop/desktop.entitlements;
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = LTZ2PFU5D6;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = desktop/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitwarden.desktop;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		55E037432577FA6B00979016 /* Build configuration list for PBXProject "desktop" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				55E037662577FA6F00979016 /* Debug */,
				55E037672577FA6F00979016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		55E037682577FA6F00979016 /* Build configuration list for PBXNativeTarget "safari" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				55E037692577FA6F00979016 /* Debug */,
				55E0376A2577FA6F00979016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		55E0376C2577FA6F00979016 /* Build configuration list for PBXNativeTarget "desktop" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				55E0376D2577FA6F00979016 /* Debug */,
				55E0376E2577FA6F00979016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 55E037402577FA6B00979016 /* Project object */;
}
