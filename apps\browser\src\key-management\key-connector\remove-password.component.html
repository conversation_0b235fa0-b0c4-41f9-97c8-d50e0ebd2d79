<header>
  <div class="left"></div>
  <div class="center">
    <span class="title">{{ "removeMasterPassword" | i18n }}</span>
  </div>
  <div class="right"></div>
</header>

<main tabindex="-1">
  <div class="box">
    <div class="box-content" *ngIf="loading">
      <div class="box-content-row">
        <i class="bwi bwi-spinner bwi-spin" title="{{ 'loading' | i18n }}" aria-hidden="true"></i>
      </div>
    </div>
    <div class="box-content" *ngIf="!loading">
      <div class="box-content-row" appBoxRow>
        <p>{{ "removeMasterPasswordForOrganizationUserKeyConnector" | i18n }}</p>
        <p class="tw-mb-0">{{ "organizationName" | i18n }}:</p>
        <p class="tw-text-muted tw-mb-6">{{ organization.name }}</p>
        <p class="tw-mb-0">{{ "keyConnectorDomain" | i18n }}:</p>
        <p class="tw-text-muted tw-mb-6">{{ organization.keyConnectorUrl }}</p>
      </div>
      <div class="box-content-row">
        <button type="button" class="btn block primary" (click)="convert()" [disabled]="action">
          <i
            class="bwi bwi-spinner bwi-spin"
            title="{{ 'loading' | i18n }}"
            aria-hidden="true"
            *ngIf="continuing"
          ></i>
          {{ "removeMasterPassword" | i18n }}
        </button>
      </div>
      <div class="box-content-row">
        <button
          type="button"
          class="btn btn-outline-secondary block"
          (click)="leave()"
          [disabled]="action"
        >
          <i
            class="bwi bwi-spinner bwi-spin"
            title="{{ 'loading' | i18n }}"
            aria-hidden="true"
            *ngIf="leaving"
          ></i>
          {{ "leaveOrganization" | i18n }}
        </button>
      </div>
    </div>
  </div>
</main>
