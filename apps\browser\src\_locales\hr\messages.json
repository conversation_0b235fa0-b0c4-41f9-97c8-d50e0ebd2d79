{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwarden upravitelj lozinki", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "<PERSON><PERSON>, na poslu ili u pokretu, Bitwarden štiti sve tvoje lozinke, pristupne ključeve i osjetljive informacije", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Prijavi se ili stvori novi račun za pristup svojem sigurnom trezoru."}, "inviteAccepted": {"message": "Pozivnica prihvaćena"}, "createAccount": {"message": "Stvori račun"}, "newToBitwarden": {"message": "Novi u Bitwardenu?"}, "logInWithPasskey": {"message": "Prijava pristupnim ključem"}, "useSingleSignOn": {"message": "Jedinstven<PERSON> (SSO)"}, "welcomeBack": {"message": "Dobro došli natrag"}, "setAStrongPassword": {"message": "<PERSON><PERSON> jaku lo<PERSON>ku"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Dovrši stvaranje svog računa postavljanjem lozinke"}, "enterpriseSingleSignOn": {"message": "Jedinstvena prijava na razini tvrtke (SSO)"}, "cancel": {"message": "Odustani"}, "close": {"message": "Zatvori"}, "submit": {"message": "Pošalji"}, "emailAddress": {"message": "Adresa e-pošte"}, "masterPass": {"message": "Glavna lozinka"}, "masterPassDesc": {"message": "Glavnu lozinku koristiš za pristup svom trezoru. Vrlo je važno da ne zaboraviš glavnu lozinku. Ne postoji način za oporavak lozinke u slučaju da ju zaboraviš."}, "masterPassHintDesc": {"message": "Podsjetnik glavne lozinke ti može pomoći da se prisjetiš svoje lozinke ako ju zaboraviš."}, "masterPassHintText": {"message": "Podsjetnik ti možemo poslati ako zaboraviš svoju lozinku. Najviše $CURRENT$/$MAXIMUM$ znakova.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Ponovno upiši glavnu lozinku"}, "masterPassHint": {"message": "Podsjetnik glavne lozinke (neobavezno)"}, "passwordStrengthScore": {"message": "<PERSON><PERSON><PERSON><PERSON> ja<PERSON><PERSON> lo<PERSON>: $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Pridruži se organizaciji"}, "joinOrganizationName": {"message": "Pidruži se $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Dovrši pridruživanje organizaciji postavljanjem glavne lozinke."}, "tab": {"message": "Kartica"}, "vault": {"message": "<PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON>"}, "allVaults": {"message": "<PERSON><PERSON> t<PERSON>ori"}, "tools": {"message": "<PERSON><PERSON>"}, "settings": {"message": "Postavke"}, "currentTab": {"message": "Trenutna kartica"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassphrase": {"message": "<PERSON><PERSON><PERSON>"}, "copyNote": {"message": "<PERSON><PERSON><PERSON>"}, "copyUri": {"message": "<PERSON><PERSON><PERSON>"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON>e"}, "copyNumber": {"message": "<PERSON><PERSON><PERSON> broj"}, "copySecurityCode": {"message": "<PERSON><PERSON><PERSON> kontrolni broj"}, "copyName": {"message": "<PERSON><PERSON><PERSON>"}, "copyCompany": {"message": "Kopiraj tvrtku"}, "copySSN": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassportNumber": {"message": "<PERSON><PERSON><PERSON> broj <PERSON>"}, "copyLicenseNumber": {"message": "<PERSON><PERSON><PERSON>"}, "copyPrivateKey": {"message": "<PERSON><PERSON><PERSON> privatni ključ"}, "copyPublicKey": {"message": "<PERSON><PERSON><PERSON> j<PERSON>"}, "copyFingerprint": {"message": "<PERSON><PERSON><PERSON> p<PERSON>a"}, "copyCustomField": {"message": "Kopiraj $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Ko<PERSON>raj web stranicu"}, "copyNotes": {"message": "<PERSON><PERSON><PERSON>"}, "copy": {"message": "<PERSON><PERSON><PERSON>", "description": "Copy to clipboard"}, "fill": {"message": "Ispuni", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Auto-ispuna"}, "autoFillLogin": {"message": "Auto-ispuna prijave"}, "autoFillCard": {"message": "Auto-ispuna kartice"}, "autoFillIdentity": {"message": "Auto-ispuna identiteta"}, "fillVerificationCode": {"message": "Ispuni kôd za provjeru"}, "fillVerificationCodeAria": {"message": "Ispuni kôd za provjeru", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON> (i kopiraj)"}, "copyElementIdentifier": {"message": "Prilagođeno ime polja"}, "noMatchingLogins": {"message": "<PERSON><PERSON> prijava"}, "noCards": {"message": "<PERSON>ema kartica"}, "noIdentities": {"message": "Nema identiteta"}, "addLoginMenu": {"message": "<PERSON><PERSON>j p<PERSON>"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON> kartic<PERSON>"}, "addIdentityMenu": {"message": "Dodaj identitet"}, "unlockVaultMenu": {"message": "Otključaj svoj trezor"}, "loginToVaultMenu": {"message": "Prijavi se u svoj trezor"}, "autoFillInfo": {"message": "Nema dostupnih prijava za auto-ispunu na web stranici u ovoj kartici."}, "addLogin": {"message": "<PERSON><PERSON>j p<PERSON>"}, "addItem": {"message": "<PERSON><PERSON>j stavku"}, "accountEmail": {"message": "e-pošta računa"}, "requestHint": {"message": "Zatraži podsjetnik"}, "requestPasswordHint": {"message": "Zatraži podsjetnik lozinke"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Unesi svoju adresu e-pošte računa i poslat ćemo ti tvoj podsjetnik"}, "getMasterPasswordHint": {"message": "Slanje podsjetnika glavne lozinke"}, "continue": {"message": "<PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Slanje verifikacijskog kôda e-poštom"}, "sendCode": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "codeSent": {"message": "<PERSON><PERSON><PERSON> p<PERSON>lan"}, "verificationCode": {"message": "<PERSON><PERSON><PERSON> za provjeru"}, "confirmIdentity": {"message": "Potvrdite lozinku za nastavak."}, "changeMasterPassword": {"message": "Promjeni glav<PERSON> lo<PERSON>"}, "continueToWebApp": {"message": "Nastavi na web aplikaciju?"}, "continueToWebAppDesc": {"message": "Pronađi više značajki svojeg Bitwarden računa u web aplikaciji."}, "continueToHelpCenter": {"message": "Nastavi u centar za pomoć?"}, "continueToHelpCenterDesc": {"message": "Za pomoć oko korištenja Bitwardena posjeti centar za pomoć."}, "continueToBrowserExtensionStore": {"message": "Nastaviti na trgovinu proširenja za preglednik?"}, "continueToBrowserExtensionStoreDesc": {"message": "<PERSON><PERSON>š preporučiti Bitwarden drugima? Posjeti trgovinu proširenja svojeg preglednika i ostavi recenziju."}, "changeMasterPasswordOnWebConfirmation": {"message": "Svoju lozinku možeš promijeniti u Bitwarden web aplikaciji."}, "fingerprintPhrase": {"message": "Jedinstvena fraza", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Jedinstvena fraza tvog računa", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Prijava dvostrukom autentifikacijom"}, "logOut": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "aboutBitwarden": {"message": "O Bitwardenu"}, "about": {"message": "O aplikaciji"}, "moreFromBitwarden": {"message": "Više od Bitwardena"}, "continueToBitwardenDotCom": {"message": "Nastavi na bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden za tvrtke"}, "bitwardenAuthenticator": {"message": "Bitwarden autentifikator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden autentifikator omogućuje pohranu autentifikatorskih ključeva i generiranje TOTP kodova za dvostruku autentifikaciju. Saznaj više na web stranici bitwarden.com"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON>, upravljaj i dijeli programerske tajne s Bitwarden Secrets Managerom. Saznajte više na web stranici bitwarden.com."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Stvori laka i sigurna iskustva prijave bez tradicionalnih lozinki uz Passwordless.dev. Saznaj više na web stranici bitwarden.com."}, "freeBitwardenFamilies": {"message": "Besplatni Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "Ispunjavaš uvjete za besplatni Bitwarden Families. Iskoristi ovu ponudu u web aplikaciji već danas."}, "version": {"message": "Verzija"}, "save": {"message": "Sp<PERSON>i"}, "move": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addFolder": {"message": "<PERSON><PERSON><PERSON> mapu"}, "name": {"message": "<PERSON><PERSON>"}, "editFolder": {"message": "<PERSON><PERSON><PERSON> mapu"}, "editFolderWithName": {"message": "Uredi mapu: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "Nova mapa"}, "folderName": {"message": "Naziv mape"}, "folderHintText": {"message": "Ugnijezdi mapu dodavanjem naziva roditeljske mape i znaka kroz. Npr. Mreže/Forumi"}, "noFoldersAdded": {"message": "Mapa nije dodana"}, "createFoldersToOrganize": {"message": "Za organiziranje stavki u trezoru, stvori mape"}, "deleteFolderPermanently": {"message": "<PERSON><PERSON><PERSON>š trajno izbrisati ovu mapu?"}, "deleteFolder": {"message": "Izbriši mapu"}, "folders": {"message": "Mape"}, "noFolders": {"message": "Nema mapa na popisu."}, "helpFeedback": {"message": "Pomoć i povratne informacije"}, "helpCenter": {"message": "Bitwarden centar za pomoć"}, "communityForums": {"message": "Istraži forume zajednice Bitwarden"}, "contactSupport": {"message": "Kontaktiraj <PERSON> pomoć"}, "sync": {"message": "Sinkronizacija"}, "syncVaultNow": {"message": "<PERSON><PERSON><PERSON> trez<PERSON>"}, "lastSync": {"message": "Posljednja sinkronizacija:"}, "passGen": {"message": "Generator lo<PERSON>ki"}, "generator": {"message": "Generator", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automatski generiraj jake, jedinstvene lo<PERSON>."}, "bitWebVaultApp": {"message": "Bitwarden web trezor"}, "importItems": {"message": "Uvoz stavki"}, "select": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassphrase": {"message": "<PERSON><PERSON><PERSON> lo<PERSON>"}, "passwordGenerated": {"message": "Lozinka generirana"}, "passphraseGenerated": {"message": "Frazna lozinka generirana"}, "usernameGenerated": {"message": "Korisničko ime generirano"}, "emailGenerated": {"message": "e-pošta generirana"}, "regeneratePassword": {"message": "Ponovno generiraj lo<PERSON>"}, "options": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "length": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "include": {"message": "Uključi", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Uključi velika slova", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Uključi mala slova", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Uključi brojeve", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Uključi posebne znakove", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON>j ri<PERSON>"}, "wordSeparator": {"message": "Razdjelitelj riječi"}, "capitalize": {"message": "Prva slova velika", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Uključi broj"}, "minNumbers": {"message": "<PERSON><PERSON><PERSON><PERSON> broje<PERSON>"}, "minSpecial": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "avoidAmbiguous": {"message": "Izbjegavaj dvosmislene <PERSON>", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Pravila tvrtke primjenjena su na generator.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "Prikaz"}, "noItemsInList": {"message": "Nema stavki za prikaz."}, "itemInformation": {"message": "Informacije o stavci"}, "username": {"message": "Korisničko ime"}, "password": {"message": "Lozinka"}, "totp": {"message": "<PERSON><PERSON><PERSON>"}, "passphrase": {"message": "<PERSON>az<PERSON>"}, "favorite": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Ukloni iz favorita"}, "itemAddedToFavorites": {"message": "Dodaj stavku u omiljene"}, "itemRemovedFromFavorites": {"message": "Stavka uklonjenja iz omiljenih"}, "notes": {"message": "Bilješke"}, "privateNote": {"message": "Privatna bilješka"}, "note": {"message": "Bilješka"}, "editItem": {"message": "Uredi stavku"}, "folder": {"message": "Mapa"}, "deleteItem": {"message": "Izbriši stavku"}, "viewItem": {"message": "Prikaz stavke"}, "launch": {"message": "Pokreni"}, "launchWebsite": {"message": "Pokreni web stranicu"}, "launchWebsiteName": {"message": "Otvori stranicu $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Web stranica"}, "toggleVisibility": {"message": "Prikaži/Sakrij"}, "manage": {"message": "Upravljanje"}, "other": {"message": "Ostalo"}, "unlockMethods": {"message": "Mogućnosti otključavanja"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "<PERSON>a promjenu vremena isteka trezora, o<PERSON><PERSON> na<PERSON> o<PERSON>."}, "unlockMethodNeeded": {"message": "Postavi način otključavanja u Postavkama"}, "sessionTimeoutHeader": {"message": "Istek sesije"}, "vaultTimeoutHeader": {"message": "<PERSON><PERSON><PERSON>"}, "otherOptions": {"message": "Ostale postavke"}, "rateExtension": {"message": "Ocijeni proširenje"}, "browserNotSupportClipboard": {"message": "Web preglednik ne podržava jednostavno kopiranje međuspremnika. Umjesto toga ručno kopirajte."}, "verifyYourIdentity": {"message": "Potvrdi svoj identitet"}, "weDontRecognizeThisDevice": {"message": "Ne prepoznajemo ovaj uređaj. Za potvrdu identiteta unesi kôd poslan e-poštom."}, "continueLoggingIn": {"message": "Nastavi prijavu"}, "yourVaultIsLocked": {"message": "Tvoj trezor je z<PERSON>l<PERSON>. Potvrdi glavnu lozinku za nastavak."}, "yourVaultIsLockedV2": {"message": "<PERSON><PERSON><PERSON> je z<PERSON>"}, "yourAccountIsLocked": {"message": "Ra<PERSON><PERSON> je z<PERSON>l<PERSON>"}, "or": {"message": "ili"}, "unlock": {"message": "Otkl<PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "Korisnički račun: $EMAIL$ na $HOSTNAME$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Neispravna glavna lozinka"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeout1": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lockNow": {"message": "Zakl<PERSON><PERSON><PERSON> sada"}, "lockAll": {"message": "Zaključaj sve"}, "immediately": {"message": "<PERSON><PERSON><PERSON>"}, "tenSeconds": {"message": "10 sekundi"}, "twentySeconds": {"message": "20 sekundi"}, "thirtySeconds": {"message": "30 sekundi"}, "oneMinute": {"message": "1 minuta"}, "twoMinutes": {"message": "2 minute"}, "fiveMinutes": {"message": "5 minuta"}, "fifteenMinutes": {"message": "15 minuta"}, "thirtyMinutes": {"message": "30 minuta"}, "oneHour": {"message": "1 sat"}, "fourHours": {"message": "4 sata"}, "onLocked": {"message": "Pri zaključavanju sustava"}, "onRestart": {"message": "Pri pokretanju preglednika"}, "never": {"message": "Nikad"}, "security": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "Potvrdi glav<PERSON> lo<PERSON>"}, "masterPassword": {"message": "Glavna lozinka"}, "masterPassImportant": {"message": "Glavnu lozinku nije moguće oporaviti ako ju zaboraviš!"}, "masterPassHintLabel": {"message": "Podsjetnik glavne lozinke"}, "errorOccurred": {"message": "<PERSON><PERSON><PERSON> je do pogreške"}, "emailRequired": {"message": "Adresa e-pošte je obavezna."}, "invalidEmail": {"message": "Neispravna adresa e-p<PERSON>š<PERSON>."}, "masterPasswordRequired": {"message": "Potrebna je glavna lozinka."}, "confirmMasterPasswordRequired": {"message": "Potreban je ponovni unos glavne lozinke."}, "masterPasswordMinlength": {"message": "Glavna lozinka mora imati najmanje $VALUE$ znakova.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Potvrda glavne lozinke se ne podudara."}, "newAccountCreated": {"message": "Tvoj novi račun je stvoren! Sada se možeš prijaviti."}, "newAccountCreated2": {"message": "Tvoj novi račun je stvoren!"}, "youHaveBeenLoggedIn": {"message": "Prijava uspješna!"}, "youSuccessfullyLoggedIn": {"message": "Prijava <PERSON>"}, "youMayCloseThisWindow": {"message": "<PERSON><PERSON><PERSON>š zatvoriti ovaj prozor"}, "masterPassSent": {"message": "Poslali smo e-poštu s podsjetnikom glavne lozinke."}, "verificationCodeRequired": {"message": "<PERSON><PERSON><PERSON> za provjeru je oba<PERSON>."}, "webauthnCancelOrTimeout": {"message": "Autentifikacija je otkazana ili je trajala predugo. Molimo pokušaj ponovno."}, "invalidVerificationCode": {"message": "Nevažeći kôd za provjeru"}, "valueCopied": {"message": " kopirano", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Nije moguće auto-ispuniti odabranu prijavu na ovoj stranici. Umjesto toga kopiraj/zalijepi podatke."}, "totpCaptureError": {"message": "<PERSON>je mog<PERSON>e skenirati QR kod s trenutne web stranice"}, "totpCaptureSuccess": {"message": "<PERSON><PERSON><PERSON>č autentifikatora je dodan"}, "totpCapture": {"message": "Skeniraj QR kôd autentifikatora s trenutne web stranice"}, "totpHelperTitle": {"message": "Učini dvostruku autentifikaciju besprijekornom"}, "totpHelper": {"message": "Bitwarden može pohraniti i ispuniti kodove za dvostruku autentifikaciju. Kopiraj i zalijepi ključ u ovo polje."}, "totpHelperWithCapture": {"message": "Bitwarden može pohraniti i ispuniti kodove za dvostruku autentifikaciju. Odaberi ikonu kamere i označi QR kôd za provjeru autentičnosti ove web stranice ili kopiraj i zalijepi ključ u ovo polje."}, "learnMoreAboutAuthenticators": {"message": "Više o autentifikatorima"}, "copyTOTP": {"message": "<PERSON><PERSON><PERSON> autentifikatora (TOTP)"}, "loggedOut": {"message": "Odjavljen"}, "loggedOutDesc": {"message": "Odjavljen/a si sa svog računa."}, "loginExpired": {"message": "Sesija je istekla."}, "logIn": {"message": "<PERSON><PERSON><PERSON><PERSON> se"}, "logInToBitwarden": {"message": "Prijavi se u Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Unesi kôd poslan e-poštom"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Unesi kôd iz svoje aplikacije za autentifikaciju"}, "pressYourYubiKeyToAuthenticate": {"message": "Za autentifikaciju dodirni svoj YubiKey"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Za tvoj je račun potrebna Duo prijava u dva koraka. Za dovršetak prijave, slijedi daljnje korake."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Prati korake za dovršetak prijave."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Ponovno pokreni registraciju"}, "expiredLink": {"message": "Istekla poveznica"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Ponovno pokreni registraciju ili se pokušaj prijaviti."}, "youMayAlreadyHaveAnAccount": {"message": "<PERSON><PERSON><PERSON> već imaš račun"}, "logOutConfirmation": {"message": "<PERSON><PERSON><PERSON> se <PERSON><PERSON> o<PERSON>?"}, "yes": {"message": "Da"}, "no": {"message": "Ne"}, "location": {"message": "Lokacija"}, "unexpectedError": {"message": "Došlo je do neočekivane pogreške."}, "nameRequired": {"message": "<PERSON>me je o<PERSON>."}, "addedFolder": {"message": "Mapa dodana"}, "twoStepLoginConfirmation": {"message": "Prijava dvostrukom autentifikacijom čini tvoj račun još sigurnijim tako što će zahtijevati potvrdu prijave drugim uređajem kao što je sigurnosni ključ, autentifikatorska aplikacija, SMS, poziv ili e-pošta. Prijavu dvostrukom autentifikacijom možeš omogućiti na web trezoru. Želiš li sada posjetiti bitwarden.com?"}, "twoStepLoginConfirmationContent": {"message": "Učini svoj račun sigurnijim uključivanjem prijave dvofaktorskom autentifikacijom u Bitwarden web aplikaciji."}, "twoStepLoginConfirmationTitle": {"message": "Nastavi na web aplikaciju?"}, "editedFolder": {"message": "Mapa spremljena"}, "deleteFolderConfirmation": {"message": "<PERSON><PERSON><PERSON> izbrisati ovu mapu?"}, "deletedFolder": {"message": "Mapa izbrisana"}, "gettingStartedTutorial": {"message": "Priručnik za početak rada"}, "gettingStartedTutorialVideo": {"message": "Pogledaj naš početni vodič za savjete kako najbolje iskoristiti proširenje preglednika."}, "syncingComplete": {"message": "Sinkronizacija dovršena"}, "syncingFailed": {"message": "Sinkronizacija nije us<PERSON>jela"}, "passwordCopied": {"message": "Lozinka kop<PERSON>na"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Novi URI"}, "addDomain": {"message": "<PERSON><PERSON><PERSON>", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Stavka do<PERSON>a"}, "editedItem": {"message": "Stavka izmijenjena"}, "deleteItemConfirmation": {"message": "<PERSON><PERSON>š li zaista poslati u smeće?"}, "deletedItem": {"message": "Stavka poslana u smeće"}, "overwritePassword": {"message": "Prebriši lozinku"}, "overwritePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> prebrisati trenutnu lozinku?"}, "overwriteUsername": {"message": "Prebriši korisničko ime"}, "overwriteUsernameConfirmation": {"message": "<PERSON><PERSON><PERSON> prebrisati trenutno korisničko ime?"}, "searchFolder": {"message": "Mapa pretraživanja"}, "searchCollection": {"message": "Pretraživanje zbirke"}, "searchType": {"message": "Tip pretrage"}, "noneFolder": {"message": "<PERSON><PERSON> mape", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Upitaj za dodavanje prijave"}, "vaultSaveOptionsTitle": {"message": "Mogućnosti spremanja u trezor"}, "addLoginNotificationDesc": {"message": "Upit za dodavanje prijave pojavljuje se kada se otkrije prva prijava na neko web mjesto. Bitwarden će te pitatati želiš li uneseno korisničko ime i lozinku spremiti u svoj trezor."}, "addLoginNotificationDescAlt": {"message": "Pitaj za dodavanje stavke ako nije pronađena u tvojem trezoru. Primjenjuje se na sve prijavljene račune."}, "showCardsInVaultViewV2": {"message": "Uvijek prikaži kartice kao prijedloge za auto-ispunu u prikazu trezora"}, "showCardsCurrentTab": {"message": "Prikaži platne kartice"}, "showCardsCurrentTabDesc": {"message": "Prikazuj platne kartice za jednostavnu auto-ispunu."}, "showIdentitiesInVaultViewV2": {"message": "Uvijek prikaži identitete kao prijedloge za auto-ispunu u prikazu trezora"}, "showIdentitiesCurrentTab": {"message": "Prikaži identitete"}, "showIdentitiesCurrentTabDesc": {"message": "Prikazuj identitete za jednostavnu auto-ispunu."}, "clickToAutofillOnVault": {"message": "Klikni stavke za auto-ispunu na prikazu trezora"}, "clickToAutofill": {"message": "Kliknite stavku u prijedlogu auto-ispune za popunjavanje"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Automatski očisti kopirane vrijednosti iz međuspremnika.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Treba li Bitwarden zapamtiti ovu lozinku?"}, "notificationAddSave": {"message": "Sp<PERSON>i"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Spremi novu prijavu", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "Prijava spreml<PERSON>na", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Greška kod spremanja", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Ups! Nismo mogli ovo spasiti. Pokušaj ručno unijeti detalje.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Upitaj za ažuriranje trenutne prijave"}, "changedPasswordNotificationDesc": {"message": "Upitaj za ažuriranje lozinke prijave ako se otkrije promjena na web stranici."}, "changedPasswordNotificationDescAlt": {"message": "Pitaj za ažuriranje lozinke za prijavu kada se otkrije promjena na web stranici. Primjenjuje se na sve prijavljene račune."}, "enableUsePasskeys": {"message": "Pitaj za spremanje/korištenje pristupnih ključeva"}, "usePasskeysDesc": {"message": "Pitaj za spremanje novih pristupnih ključeva ili se prijavi pomoću pristupnih ključeva pohranjenih u tvojem trezoru. Primjenjuje se na sve prijavljene račune."}, "notificationChangeDesc": {"message": "<PERSON><PERSON><PERSON> li ovu lozinku ažurirati u Bitwarden-u?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "Za dovršetak auto-ispune, otključaj svoj Bitwarden trezor."}, "notificationUnlock": {"message": "Otkl<PERSON><PERSON><PERSON>"}, "additionalOptions": {"message": "Dodatne m<PERSON>ć<PERSON>ti"}, "enableContextMenuItem": {"message": "Prikaži opcije kotekstualnog izbornika"}, "contextMenuItemDesc": {"message": "Koristi sekundarni klik za pristup generatoru lozinki i pripadajućim prijavama trenunte web stranice. "}, "contextMenuItemDescAlt": {"message": "Koristi sekundarni klik za pristup generiranju lozinki i odgovarajućim prijavama za mrežno mjesto. Primjenjuje se na sve prijavljene račune."}, "defaultUriMatchDetection": {"message": "Zadano otkrivanje URI podudaranja", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Odaberi zadani način na koji će se riješavati otkrivanje URI-ja za prijavu pri izvođenju radnji kao što je auto-ispuna."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> temu boja."}, "themeDescAlt": {"message": "Promijeni boju aplikacije. Primjenjuje se na sve prijavljene račune."}, "dark": {"message": "Tam<PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Izvezi iz"}, "exportVault": {"message": "<PERSON><PERSON><PERSON><PERSON> trez<PERSON>"}, "fileFormat": {"message": "Format datoteke"}, "fileEncryptedExportWarningDesc": {"message": "Ova izvozna datoteka biti će zaštićena lozinkom bez koje ju neće biti moguće dešifrirati."}, "filePassword": {"message": "<PERSON>zinka datoteke"}, "exportPasswordDescription": {"message": "Ova će se lozinka koristiti za izvoz i uvoz ove datoteke"}, "accountRestrictedOptionDescription": {"message": "Upotrijebi svoj ključ za šifriranje računa, izveden iz korisničkog imena i glavne lozinke za šifriranje izvoza i ograničavanje uvoza samo na trenutni Bitwarden račun."}, "passwordProtectedOptionDescription": {"message": "Bitwarden omogućuje dijeljenje trezora s drugima pomoću organizacijskog računa. Za više informacija posjeti bitwarden. com."}, "exportTypeHeading": {"message": "Tip izvoza"}, "accountRestricted": {"message": "<PERSON><PERSON><PERSON>"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "Lozinka se ne podudara."}, "warning": {"message": "UPOZORENJE", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Upozorenje", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Potvrdi izvoz trezora"}, "exportWarningDesc": {"message": "Ovaj izvoz sadrži podatke trezora u nešifriranom obliku! Izvezenu datoteku se ne bi smjelo pohranjivati ili slati putem nesigurnih kanala (npr. e-poštom). Izbriši ju odmah nakon završetka korištenja."}, "encExportKeyWarningDesc": {"message": "Ovaj izvoz šifrira tvoje podatke koristeći ključ za šifriranje. Promije<PERSON>š li naknadno ključ za šifriranje, potrebno je ponovno napraviti izvoz jer nećeš moći dešifrirati ovu izvezenu datoteku."}, "encExportAccountWarningDesc": {"message": "Ključ za šifriranje jedinstven je za svakog Bitwarden korisnika, kako bi se šifrirani izvoz mogao uvesti u drugi korisnički račun."}, "exportMasterPassword": {"message": "Unesi glavnu lozinku za izvoz podataka iz trezora."}, "shared": {"message": "Dijeljeno"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business omogućuje dijeljenje stavki trezora s drugima koristeći organizacije. Za više informacija posjeti bitwarden.com."}, "moveToOrganization": {"message": "Premjesti u organizaciju"}, "movedItemToOrg": {"message": "$ITEMNAME$ premješteno u $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Odaberi organizaciju u koju želiš premjestiti ovu stavku. Premještanje prenosi vlasništvo stavke na organizaciju. Nakon premještanja više nećeš biti izravni vlasnik ove stavke."}, "learnMore": {"message": "Saznaj više"}, "authenticatorKeyTotp": {"message": "K<PERSON><PERSON><PERSON> autentifikatora (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON><PERSON> za <PERSON> (TOTP)"}, "copyVerificationCode": {"message": "<PERSON><PERSON><PERSON> k<PERSON> za provjeru"}, "attachments": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deleteAttachment": {"message": "Izbriši privitak"}, "deleteAttachmentConfirmation": {"message": "<PERSON><PERSON><PERSON> ž<PERSON>š izbrisati ovaj privitak?"}, "deletedAttachment": {"message": "Privitak izbrisan"}, "newAttachment": {"message": "Dodaj novi privitak"}, "noAttachments": {"message": "<PERSON><PERSON> privi<PERSON>."}, "attachmentSaved": {"message": "Privitak spremljen"}, "file": {"message": "Datoteka"}, "fileToShare": {"message": "Datoteka za dijeljenje"}, "selectFile": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>."}, "maxFileSize": {"message": "Najveća veličina datoteke je 500 MB."}, "featureUnavailable": {"message": "Značajka nije dostu<PERSON>na"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Premium članstvo"}, "premiumManage": {"message": "Upravljaj članstvom"}, "premiumManageAlert": {"message": "Svojim članstvom možeš upravljati na web trezoru. Želiš li sada posjetiti bitwarden.com?"}, "premiumRefresh": {"message": "Osvježi status članstva"}, "premiumNotCurrentMember": {"message": "Trenutno nisi premium član."}, "premiumSignUpAndGet": {"message": "Prijavi se za premium članstvo, čime dobivaš:"}, "ppremiumSignUpStorage": {"message": "1 GB šifriranog prostora za pohranu podataka."}, "premiumSignUpEmergency": {"message": "Pristup u nuždi."}, "premiumSignUpTwoStepOptions": {"message": "Mogućnosti za prijavu u dva koraka kao što su YubiKey i Duo."}, "ppremiumSignUpReports": {"message": "Higijenu lozinki, zdravlje računa i izvještaje o krađi podatak radi zaštite svojeg trezora."}, "ppremiumSignUpTotp": {"message": "Generator TOTP kontrolnog koda (2FA) za prijave u tvom trezoru."}, "ppremiumSignUpSupport": {"message": "Prioritetnu korisničku podršku."}, "ppremiumSignUpFuture": {"message": "Sve buduće premium značajke. Uskoro više!"}, "premiumPurchase": {"message": "Kupi premium članstvo"}, "premiumPurchaseAlertV2": {"message": "Premium možeš kupiti u postavkama računa na Bitwarden web aplikaciji."}, "premiumCurrentMember": {"message": "Ti si premium član!"}, "premiumCurrentMemberThanks": {"message": "Hvala ti <PERSON> pod<PERSON> Bitwarden."}, "premiumFeatures": {"message": "Nadogradi na Premium za:"}, "premiumPrice": {"message": "Sve za samo $PRICE$ /godišnje!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Sve samo za $PRICE$ /godišnje!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Osvježavanje završeno"}, "enableAutoTotpCopy": {"message": "Automatski kopiraj TOTP"}, "disableAutoTotpCopyDesc": {"message": "Ako za prijavu postoji ključ autentifikatora, kopiraj TOTP kôd za provjeru u međuspremnik nakon auto-ispune prijave."}, "enableAutoBiometricsPrompt": {"message": "Traži biometrijsku autentifikaciju pri pokretanju"}, "premiumRequired": {"message": "Potrebno premium članstvo"}, "premiumRequiredDesc": {"message": "Za korištenje ove značajke potrebno je Premium članstvo."}, "authenticationTimeout": {"message": "Istek vremena za autentifikaciju"}, "authenticationSessionTimedOut": {"message": "Sesija za autentifikaciju je istekla. Ponovi proces prijave."}, "verificationCodeEmailSent": {"message": "E-pošta za potvrdu poslana je na $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Ne pitaj na ovom uređaju idućih 30 dana"}, "selectAnotherMethod": {"message": "Odaberi drugi način", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "<PERSON><PERSON><PERSON> k<PERSON> za oporavak"}, "insertU2f": {"message": "Umetni svoj sigurnosni ključ u USB priključak računala. Ako ima tipku, dodirni ju."}, "openInNewTab": {"message": "Otvori u novoj kartici"}, "webAuthnAuthenticate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "readSecurityKey": {"message": "Pročitaj sigurnosni ključ"}, "awaitingSecurityKeyInteraction": {"message": "Čekanje na interakciju sa sigurnosnim ključem..."}, "loginUnavailable": {"message": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>"}, "noTwoStepProviders": {"message": "Ovaj račun ima omogućenu prijavu dvostrukom autentifikacijom, međutim ovaj web preglednik ne podržava niti jednog konfiguriranog pružatelja dvostruke autentifikacije."}, "noTwoStepProviders2": {"message": "<PERSON><PERSON><PERSON>ržani web-preglednik (npr. Chrome) i/ili dodaj dodatne usluge koje su bolje podržane u web preglednicima (npr. aplikacija Autentifikator)."}, "twoStepOptions": {"message": "Mogućnosti prijave dvostrukom autentifikacijom"}, "selectTwoStepLoginMethod": {"message": "Odaberi način prijave dvostrukom autentifikacijom"}, "recoveryCodeDesc": {"message": "Izgubljen je pristup uređaju za dvostruku autentifikaciju? Koristi svoj kôd za oporavak za onemogućavanje svih pružatelja usluga dvostruke autentifikacije na tvojem računu."}, "recoveryCodeTitle": {"message": "Kôd za oporavak"}, "authenticatorAppTitle": {"message": "Autentifikatorska aplikacija"}, "authenticatorAppDescV2": {"message": "Unesi kôd generiran autentifikatorskom aplikacijom kao npr. Bitwarden Authenticatorom.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP sigurnosni ključ"}, "yubiKeyDesc": {"message": "<PERSON><PERSON>i Yu<PERSON> za pristup svojem računu. <PERSON><PERSON> s YubiKey 4, 4 <PERSON><PERSON>, 4C i NEO uređajima."}, "duoDescV2": {"message": "<PERSON><PERSON> kôd generiran <PERSON>.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Potvrdi s Duo Security za svoju organizaciju pomoću aplikacije Duo Mobile, SMS-om, telefonskim pozivom ili U2F sigurnosnim ključem.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Koristi WebAuthn sigurnosni ključ za pristup svojem računu."}, "emailTitle": {"message": "E-pošta"}, "emailDescV2": {"message": "Unesi kôd poslan e-p<PERSON>š<PERSON>."}, "selfHostedEnvironment": {"message": "Vlastito hosting okruženje"}, "selfHostedBaseUrlHint": {"message": "<PERSON><PERSON><PERSON> osnovni URL svoje lokalne Bitwarden instalacije, npr.: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> odrediti osnovni URL svake usluge zasebno."}, "selfHostedEnvFormInvalid": {"message": "Moraš dodati ili osnovni URL poslužitelja ili barem jedno prilagođeno okruženje."}, "customEnvironment": {"message": "Prilagođeno okruženje"}, "baseUrl": {"message": "URL poslužitelja"}, "selfHostBaseUrl": {"message": "URL vlastitog poslužitelja", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "URL API poslužitelja"}, "webVaultUrl": {"message": "URL poslužitelja web trezora"}, "identityUrl": {"message": "URL id poslužitelja"}, "notificationsUrl": {"message": "URL poslužitelja obavijesti"}, "iconsUrl": {"message": "URL poslužitelja ikona"}, "environmentSaved": {"message": "URL-ovi okoline su spremljeni."}, "showAutoFillMenuOnFormFields": {"message": "Prikaži izbornik za auto-ispunu u poljima obrasca", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Prijedlozi auto-ispune"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Prikaži prijedloge auto-ispune na poljima obrazaca"}, "showInlineMenuIdentitiesLabel": {"message": "Prikaži identitete kao prijedloge"}, "showInlineMenuCardsLabel": {"message": "Prikaži platne kartice kao prijedloge"}, "showInlineMenuOnIconSelectionLabel": {"message": "Prikaži prijedloge kada je odabrana ikona"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Primjenjuje se na sve prijavljene račune."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Isključi preglednikov upravitelj lozinki kako bi izbjegli sukobe."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Uredi postavke preglednika."}, "autofillOverlayVisibilityOff": {"message": "Isključeno", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "Kada je odabrano polje (u fokusu)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "Kada je odabrana ikona auto-ispune", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Auto-ispuna kod učitavanja stranice"}, "enableAutoFillOnPageLoad": {"message": "Auto-ispuna kod učitavanja"}, "enableAutoFillOnPageLoadDesc": {"message": "Nakon učitavanja web stranice, ako je otkriven obrazac za prijavu, auto-ispuni."}, "experimentalFeature": {"message": "Ugrožene ili nepouzdane web stranice mogu iskoristiti auto-ispunu prilikom učitavanja stranice."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Saznaj više o rizicima"}, "learnMoreAboutAutofill": {"message": "Saznaj više o auto-ispuni"}, "defaultAutoFillOnPageLoad": {"message": "Zadana postvaka Auto-ispune za prijave"}, "defaultAutoFillOnPageLoadDesc": {"message": "Auto-ispunu kod učitavanju stranice je moguće uključiti/isključiti za svaku pojedinu prijavu unutar uređivanja stavke."}, "itemAutoFillOnPageLoad": {"message": "Auto-ispuna kod učitavanja stranice (ako je uključeno u Postavkama)"}, "autoFillOnPageLoadUseDefault": {"message": "<PERSON><PERSON><PERSON>"}, "autoFillOnPageLoadYes": {"message": "Auto-ispuna kod učitavanja stranice"}, "autoFillOnPageLoadNo": {"message": "Ne koristi auto-ispunu kod učitavanja stranice"}, "commandOpenPopup": {"message": "Otvori iskočni prozor trezora"}, "commandOpenSidebar": {"message": "Otvori trezor u bočnoj traci"}, "commandAutofillLoginDesc": {"message": "Auto-ispuni zadnje korištenu prijavu za trenutnu web stranicu"}, "commandAutofillCardDesc": {"message": "Auto-ispuni zadnje korištenu karticu za trenutnu web stranicu"}, "commandAutofillIdentityDesc": {"message": "Auto-ispuni zadnje korišteni identitet za trenutnu web stranicu"}, "commandGeneratePasswordDesc": {"message": "Generiraj i kopiraj novu nasumičnu lozinku u međuspremnik."}, "commandLockVaultDesc": {"message": "Zaključ<PERSON> trezor"}, "customFields": {"message": "Prilagođena polja"}, "copyValue": {"message": "<PERSON><PERSON>raj vrijednost"}, "value": {"message": "Vrijednost"}, "newCustomField": {"message": "Novo prilagođeno polje"}, "dragToSort": {"message": "Povuci za sortiranje"}, "dragToReorder": {"message": "Povuci za premještanje"}, "cfTypeText": {"message": "Tekst"}, "cfTypeHidden": {"message": "Skriveno"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Potvrdni okvir"}, "cfTypeLinked": {"message": "Povezano", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Povezana vrijednost", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "<PERSON><PERSON> k<PERSON> izvan iskočnog prozora, za provjeru kôda za provjeru iz e-pošte, on će se zatvoriti. <PERSON><PERSON><PERSON> li ovaj iskočni prozor otvoriti u novom prozoru kako se ne bi zatvorio?"}, "popupU2fCloseMessage": {"message": "Ovaj preglednik ne može obraditi U2F zahtjeve u ovom iskočnom prozoru. <PERSON><PERSON><PERSON> li otvoriti ovaj iskočni prozor u novom prozoru za prijavu putem U2F?"}, "enableFavicon": {"message": "Prikaži ikone mrežnih mjesta"}, "faviconDesc": {"message": "Prikaži prepoznatljivu sliku pored svake prijave."}, "faviconDescAlt": {"message": "Prikaži prepoznatljivu sliku pokraj svake prijave. Primjenjuje se na sve prijavljene račune."}, "enableBadgeCounter": {"message": "Prikaži značku brojača"}, "badgeCounterDesc": {"message": "Prikazuje broj spremljenih prijava za trenutnu web stranicu."}, "cardholderName": {"message": "Vlasnik platne kartice"}, "number": {"message": "<PERSON><PERSON><PERSON>"}, "brand": {"message": "Vrsta kartice"}, "expirationMonth": {"message": "Mjesec isteka"}, "expirationYear": {"message": "<PERSON><PERSON>"}, "expiration": {"message": "Istek"}, "january": {"message": "siječ<PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "april": {"message": "travanj"}, "may": {"message": "sviban<PERSON>"}, "june": {"message": "<PERSON><PERSON><PERSON>"}, "july": {"message": "srpanj"}, "august": {"message": "kolovoz"}, "september": {"message": "rujan"}, "october": {"message": "listopad"}, "november": {"message": "studeni"}, "december": {"message": "prosinac"}, "securityCode": {"message": "Sigurnos<PERSON> kôd"}, "ex": {"message": "npr."}, "title": {"message": "Titula"}, "mr": {"message": "g."}, "mrs": {"message": "g<PERSON>a."}, "ms": {"message": "g<PERSON><PERSON>."}, "dr": {"message": "dr."}, "mx": {"message": "gx."}, "firstName": {"message": "Ime"}, "middleName": {"message": "Srednje ime"}, "lastName": {"message": "Prezime"}, "fullName": {"message": "<PERSON><PERSON> i prezime"}, "identityName": {"message": "Ime identiteta"}, "company": {"message": "Tvrtka"}, "ssn": {"message": "OIB"}, "passportNumber": {"message": "<PERSON><PERSON><PERSON>"}, "licenseNumber": {"message": "B<PERSON>j osobne iskaznice"}, "email": {"message": "E-pošta"}, "phone": {"message": "Telefon"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Adresa 1"}, "address2": {"message": "Adresa 2"}, "address3": {"message": "Adresa 3"}, "cityTown": {"message": "Grad / Mjesto"}, "stateProvince": {"message": "Država / Pokrajina"}, "zipPostalCode": {"message": "Poštanski broj"}, "country": {"message": "Zemlja"}, "type": {"message": "Vrsta"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "Prijave"}, "typeSecureNote": {"message": "Sigurna bilješka"}, "typeCard": {"message": "<PERSON>lat<PERSON> kartica"}, "typeIdentity": {"message": "Identitet"}, "typeSshKey": {"message": "SSH ključ"}, "newItemHeader": {"message": "Novi $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Uredi $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Pogledaj $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generatorHistory": {"message": "Povijest generatora"}, "clearGeneratorHistoryTitle": {"message": "Očisti povijest generatora"}, "cleargGeneratorHistoryDescription": {"message": "Cijela povijest generatora biti će trajno izbirsana. <PERSON><PERSON><PERSON> nastavi<PERSON>?"}, "back": {"message": "Natrag"}, "collections": {"message": "Zbirke"}, "nCollections": {"message": "$COUNT$ zbirki", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popOutNewWindow": {"message": "Otvori u novom prozoru"}, "refresh": {"message": "Osvježi"}, "cards": {"message": "Platne kartice"}, "identities": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "logins": {"message": "Prijave"}, "secureNotes": {"message": "<PERSON><PERSON><PERSON>"}, "sshKeys": {"message": "SSH ključevi"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "Provjeri je li lozinka bila ukradena."}, "passwordExposed": {"message": "Ova lozinka je otkrivena $VALUE$ put(a) prilikom krađe podataka. Trebalo bi ju promijeniti.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Lozinka nije pronađena niti u jednoj krađi podataka. Sigurna je za korištenje."}, "baseDomain": {"message": "<PERSON><PERSON><PERSON>na", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Osnovna domena (preporučeno)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON> domene", "description": "Domain name. Ex. website.com"}, "host": {"message": "Host", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Točno"}, "startsWith": {"message": "Počinje s"}, "regEx": {"message": "Regularni izraz", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Otkrivanje podudaranja", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Zadano otkrivanje podudaranja", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Uključi/isključi opcije"}, "toggleCurrentUris": {"message": "Uključi/Isključi trenutne URI", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Trenutni URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organizacija", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON>"}, "allItems": {"message": "Sve stavke"}, "noPasswordsInList": {"message": "Nema lozinki na popisu."}, "clearHistory": {"message": "<PERSON><PERSON><PERSON><PERSON> povije<PERSON>"}, "nothingToShow": {"message": "<PERSON>š<PERSON> za p<PERSON>zati"}, "nothingGeneratedRecently": {"message": "<PERSON><PERSON><PERSON> nije generi<PERSON>"}, "remove": {"message": "Ukloni"}, "default": {"message": "Zadano"}, "dateUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "<PERSON><PERSON><PERSON><PERSON> koristiti opciju „Nikada”? Postavljanje opcija zaključavanja na „Nikada” pohranjuje šifru tvojeg trezora na tvom uređaju. <PERSON><PERSON> koristi<PERSON> ovu opciju, trebali bi osigurati da je uređaj pravilno zaštićen."}, "noOrganizationsList": {"message": "Ne pripadaš niti jednoj organizaciji. Organizacije omogućuju sigurno dijeljenje stavki s drugim korisnic<PERSON>."}, "noCollectionsInList": {"message": "Nema zbirki za prikaz."}, "ownership": {"message": "Vlasništvo"}, "whoOwnsThisItem": {"message": "Tko je vlasnik ove stavke?"}, "strong": {"message": "<PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "Dobra", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "Slaba", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Slaba glavna lozinka"}, "weakMasterPasswordDesc": {"message": "Odabrana glavna lozinka je slaba. <PERSON><PERSON><PERSON>š koristiti jaču glavnu lozinku (ili frazu) kako bi tvoj Bitwarden račun bio pravilno zaštićen. <PERSON><PERSON><PERSON> koristiti ovakvu, slabu glavnu lozinku?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Otključaj PIN-om"}, "setYourPinTitle": {"message": "Postavi <PERSON>"}, "setYourPinButton": {"message": "Postavi <PERSON>"}, "setYourPinCode": {"message": "Postavi svoj PIN kôd za otključavanje Bitwardena. Postavke PIN-a se resetiraju ako se potpuno odjaviš iz aplikacije."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "Potreban je PIN."}, "invalidPin": {"message": "Nerispravan PIN."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Previše netočnih pokušaja unosa PIN-a. <PERSON>..."}, "unlockWithBiometrics": {"message": "Otključaj biometrijom"}, "unlockWithMasterPassword": {"message": "Otključaj glavnom lozinkom"}, "awaitDesktop": {"message": "Čekanje potvrde iz desktop aplikacije"}, "awaitDesktopDesc": {"message": "Potvrdi korištenje biometrije u Bitwarden desktop aplikaciji za korištenje biometrije u pregledniku."}, "lockWithMasterPassOnRestart": {"message": "Zaključaj glavnom lozinkom kod svakog pokretanja preglednika"}, "lockWithMasterPassOnRestart1": {"message": "Zaključaj glavnom lozinkom kod svakog pokretanja preglednika"}, "selectOneCollection": {"message": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu zbir<PERSON>."}, "cloneItem": {"message": "Kloniraj stavku"}, "clone": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordGenerator": {"message": "Generator lo<PERSON>ki"}, "usernameGenerator": {"message": "Generator k<PERSON><PERSON><PERSON><PERSON><PERSON> imena"}, "useThisEmail": {"message": "<PERSON><PERSON><PERSON> ovu e-poš<PERSON>"}, "useThisPassword": {"message": "<PERSON><PERSON><PERSON> ovu lo<PERSON>ku"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Koristi ovo korisničko ime"}, "securePasswordGenerated": {"message": "Sigurna lozinka generirana! Ne zaboravi ažurirati lozinku na web stranici."}, "useGeneratorHelpTextPartOne": {"message": "<PERSON><PERSON><PERSON> generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "za stvaranje s<PERSON>, jedinstvene lozinke", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Prilagodba trezora"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON> isteka trezora"}, "vaultTimeoutAction1": {"message": "<PERSON><PERSON><PERSON> isteka "}, "lock": {"message": "Zak<PERSON><PERSON><PERSON><PERSON>", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Smeć<PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Pretraži smeće"}, "permanentlyDeleteItem": {"message": "Trajno izbriši stavku"}, "permanentlyDeleteItemConfirmation": {"message": "<PERSON><PERSON><PERSON> trajno izbrisati ovu stavku?"}, "permanentlyDeletedItem": {"message": "Stavka trajno izbrisana"}, "restoreItem": {"message": "<PERSON><PERSON>i stavku"}, "restoredItem": {"message": "Stavka vraćena"}, "alreadyHaveAccount": {"message": "Već imaš račun?"}, "vaultTimeoutLogOutConfirmation": {"message": "Odjava će ukloniti pristup tvom trezoru i zahtijevati mrežnu potvrdu identiteta nakon isteka vremenske neaktivnosti. <PERSON><PERSON><PERSON> koristiti ovu postavku?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Potvrda radnje nakon vremenske neaktivnosti"}, "autoFillAndSave": {"message": "Auto-ispuni i spremi"}, "fillAndSave": {"message": "Ispuni i spremi"}, "autoFillSuccessAndSavedUri": {"message": "Stavka auto-ispunjena i spremljen URI"}, "autoFillSuccess": {"message": "Stavka je auto-ispunjena "}, "insecurePageWarning": {"message": "Upozorenje: O<PERSON> je nezaštićena HTTP stranica i svi podaci koje preko nje pošalješ drugi mogu vidjeti i izmijeniti. Ova prijava je prvotno bila spremljena za sigurnu (HTTPS) stranicu."}, "insecurePageWarningFillPrompt": {"message": "Želiš li i dalje ispuniti ove podatke za prijavu?"}, "autofillIframeWarning": {"message": "Obrazac je na poslužitelju koji se nalazi na drugačijoj domeni od URI-a za koji su spremljeni tvoji podaci za pristup. Odobri za auto-ispunu ili odustani za prekid."}, "autofillIframeWarningTip": {"message": "Ka<PERSON> se ovo upozorenje ubuduće ne bi prikazivalo, spremi ovaj URI ( $HOSTNAME$) u svoju stavku za prijavu.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON><PERSON> g<PERSON> lo<PERSON>"}, "currentMasterPass": {"message": "Trenutna glavna lozinka"}, "newMasterPass": {"message": "Nova glavna lozinka"}, "confirmNewMasterPass": {"message": "Potvrdi novu glavnu lozinku"}, "masterPasswordPolicyInEffect": {"message": "Jedno ili više pravila organizacije zahtijeva da tvoja glavna lozinka ispunjava sljedeće uvjete:"}, "policyInEffectMinComplexity": {"message": "Minimalna ocjena složenosti od $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Duljina najmanje $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Sad<PERSON>ži jedno ili više velikih slova"}, "policyInEffectLowercase": {"message": "<PERSON><PERSON><PERSON><PERSON> jedno ili više malih slova"}, "policyInEffectNumbers": {"message": "<PERSON><PERSON><PERSON><PERSON> jedan ili više brojeva"}, "policyInEffectSpecial": {"message": "<PERSON><PERSON><PERSON><PERSON> jedan ili više sljedećih posebnih znakova: $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Tvoja nova glavna lozinka ne ispunjava zahtjeve."}, "receiveMarketingEmailsV2": {"message": "Primaj e-poštom od Bitwardena savjete, najave i mogućnosti istraživanja."}, "unsubscribe": {"message": "Poništi pretplatu"}, "atAnyTime": {"message": "bilo kada."}, "byContinuingYouAgreeToThe": {"message": "<PERSON><PERSON>, s<PERSON>ž<PERSON>š se s"}, "and": {"message": "i"}, "acceptPolicies": {"message": "Označavanjem ove kućice slažete se sa sljedećim:"}, "acceptPoliciesRequired": {"message": "Uvjeti korištenja i Politika privatnosti nisu prihvaćeni."}, "termsOfService": {"message": "Uvjeti korištenja"}, "privacyPolicy": {"message": "Pravila privatnosti"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Podsjetnik za lozinku ne može biti isti kao lozinka."}, "ok": {"message": "U redu"}, "errorRefreshingAccessToken": {"message": "Pogreška osvježavanja tokena pristupa"}, "errorRefreshingAccessTokenDesc": {"message": "Nije pronađ<PERSON> token za osvježavanje ili API ključevi. Pokušaj se odjaviti i ponovno prijaviti."}, "desktopSyncVerificationTitle": {"message": "Potvrda desktop sinkronizacije"}, "desktopIntegrationVerificationText": {"message": "Provjer<PERSON> da desktop aplikacija prikazuje ovaj otisak:"}, "desktopIntegrationDisabledTitle": {"message": "Integracija s preglednikom nije omogućena"}, "desktopIntegrationDisabledDesc": {"message": "Integracija s preglednikom nije omogućena u Bitwarden desktop aplikaciji. Omogući integraciju u postavkama desktop aplikacije."}, "startDesktopTitle": {"message": "Pokreni Bitwarden dekstop aplikaciju"}, "startDesktopDesc": {"message": "Bitwarden desktop aplikacija mora biti pokrenuta prije korištenja ove funkcije."}, "errorEnableBiometricTitle": {"message": "<PERSON>je mogu<PERSON>e omogućiti biometriju"}, "errorEnableBiometricDesc": {"message": "Desktop aplikacija je poništila akciju"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Desktop aplikacija je onemogućila sigurni komunikacijski kanal. Molimo, pokušaj ponovno."}, "nativeMessagingInvalidEncryptionTitle": {"message": "Desktop komunikacija prekinuta"}, "nativeMessagingWrongUserDesc": {"message": "Desktop aplikacija je prijavl<PERSON>na, ali s <PERSON>im korisničkim računom. Provjeri da obje aplikacije koriste isti korisnički račun."}, "nativeMessagingWrongUserTitle": {"message": "Pogrešan korisnički račun"}, "nativeMessagingWrongUserKeyTitle": {"message": "Neusklađenost biometrijskog ključa"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometrijsko otključavanje nije uspjelo. Biometrijski tajni ključ nije uspio otključati trezor. Pokušaj ponovo postaviti biometriju."}, "biometricsNotEnabledTitle": {"message": "Biometrija nije omogućena"}, "biometricsNotEnabledDesc": {"message": "Biometrija preglednika zahtijeva prethodno omogućenu biometriju u Bitwarden desktop aplikaciji."}, "biometricsNotSupportedTitle": {"message": "Biometrija nije podržana"}, "biometricsNotSupportedDesc": {"message": "Biometrija preglednika nije podržana na ovom uređaju."}, "biometricsNotUnlockedTitle": {"message": "Korisnik zaključan ili odjavljen"}, "biometricsNotUnlockedDesc": {"message": "Otključaj ovog korisnika u desktop aplikaciji i pokušaj ponovno."}, "biometricsNotAvailableTitle": {"message": "Biometrijsko otključavanje nije dostupno"}, "biometricsNotAvailableDesc": {"message": "Biometrijsko otključavanje trenutno nije dostupno. Pokušaj ponovno kasnije."}, "biometricsFailedTitle": {"message": "Biometrija neuspješna"}, "biometricsFailedDesc": {"message": "Biometrija se ne može dovršiti. Pokušaj glavnom lozinkom ili se odjavi i ponovno prijavi. Ako se ovo nastavi, obrati se Bitwarden podršci."}, "nativeMessaginPermissionErrorTitle": {"message": "Dopuštenje nije dano"}, "nativeMessaginPermissionErrorDesc": {"message": "Bez odobrenja za komunikaciju s Bitwarden Desktop aplikacijom nije moguće korištenje biometrije u proširenju preglednika."}, "nativeMessaginPermissionSidebarTitle": {"message": "Greška zahtjeva dozvole"}, "nativeMessaginPermissionSidebarDesc": {"message": "Ovu ranju nije moguće napraviti u bočnom meniju. Pokušaj ponovno u iskočnom prozoru."}, "personalOwnershipSubmitError": {"message": "Pravila tvrtke onemogućuju spremanje stavki u osobni trezor. Promijeni vlasništvo stavke na tvrtku i odaberi dostupnu Zbirku."}, "personalOwnershipPolicyInEffect": {"message": "Pravila organizacije utječu na tvoje mogućnosti vlasništva. "}, "personalOwnershipPolicyInEffectImports": {"message": "Organizacijsko pravilo onemogućuje uvoz stavki u tvoj osobni trezor."}, "domainsTitle": {"message": "<PERSON><PERSON>", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blokirane domene"}, "learnMoreAboutBlockedDomains": {"message": "Saznaj više o blokiranim domenama"}, "excludedDomains": {"message": "Izuzete domene"}, "excludedDomainsDesc": {"message": "Bitwarden neće pitati treba li spremiti prijavne podatke za ove domene. Za primje<PERSON> promje<PERSON>, potrebno je osvježiti stranicu."}, "excludedDomainsDescAlt": {"message": "Bitwarden neće nuditi spremanje podataka za prijavu za ove domene za sve prijavljene račune. Moraš osvježiti stranicu kako bi promjene stupile na snagu."}, "blockedDomainsDesc": {"message": "Auto-ispuna i druge vezane značajke neće biti ponuđene za ova web mjesta. Potrebno je osvježiti stranicu zaprimjenu postavki."}, "autofillBlockedNoticeV2": {"message": "Auto-ispuna je blokirana za ovu web stranicu."}, "autofillBlockedNoticeGuidance": {"message": "Promijeni ovo u postavkama"}, "change": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Promijeni lozinku - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "Rizič<PERSON> lo<PERSON>"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ traži da promijeniš jednu rizičnu lozinku.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "Broj rizičnih lozinki koje $ORGANIZATION$ traži da promijeniš: $COUNT$.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Broj rizičnih lozinki koje tvoja orgnaizacija traži da promijeniš: $COUNT$.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Pregledaj i promijeni jednu rizičnu lozinku"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Broj rizičnih lozinki za pregled i promjenu: $COUNT$", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Brže promijeni rizične lozinke"}, "changeAtRiskPasswordsFasterDesc": {"message": "Ažuriraj svoje postavke kako za brzu auto-ispunu svojih lozinki i generiranje novih"}, "reviewAtRiskLogins": {"message": "Pregledaj rizične prijave"}, "reviewAtRiskPasswords": {"message": "Pregdledaj rizične lozin<PERSON>"}, "reviewAtRiskLoginsSlideDesc": {"message": "Lozinke tvoje organizacije su rizične jer su slabe, nanovo korištene i/ili iscurile.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "<PERSON><PERSON><PERSON> jake, jedinstvene lozinke koristeći Bitwarden dijalog auto-ispune direktno na stranici.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Ažuriraj u Bitwardenu"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden će te pitati treba li ažurirati lozinku u upravitelju lozinki.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Uključi auto-ispunu"}, "turnedOnAutofill": {"message": "Auto-ispuna uključena"}, "dismiss": {"message": "<PERSON>d<PERSON><PERSON>"}, "websiteItemLabel": {"message": "Web stranica $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ nije valjana domena", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Spremljene promjene blo<PERSON>ranih domena"}, "excludedDomainsSavedSuccess": {"message": "Spremljene promjene izuzete domene"}, "limitSendViews": {"message": "Ograniči broj pogleda"}, "limitSendViewsHint": {"message": "<PERSON><PERSON> br<PERSON>, nitko neće moći pogledati <PERSON>", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "Preostalo pogleda: $ACCESSCOUNT$", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Tekst"}, "sendTypeTextToShare": {"message": "Dijeljeni tekst"}, "sendTypeFile": {"message": "Datoteka"}, "allSends": {"message": "<PERSON><PERSON>ovi", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Zadano sakrij tekst"}, "expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordProtected": {"message": "Zaštićeno lozinkom"}, "copyLink": {"message": "<PERSON><PERSON><PERSON>"}, "copySendLink": {"message": "<PERSON><PERSON><PERSON> ve<PERSON> na <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Ukloni lozinku"}, "delete": {"message": "Izbriši"}, "removedPassword": {"message": "Lozinka uklonjena"}, "deletedSend": {"message": "Send iz<PERSON>san", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Veza na Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "removePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> ž<PERSON>š ukloniti lozinku?"}, "deleteSend": {"message": "Izbriš<PERSON> Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "<PERSON><PERSON><PERSON> ž<PERSON>š izbrisati ovaj Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "<PERSON><PERSON><PERSON> ž<PERSON>š trajno izbrisati ovaj Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Obriši za"}, "deletionDateDescV2": {"message": "Send će na ovaj datum biti trajno izbrisan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Vremenski ograničeni pristup"}, "oneDay": {"message": "1 dan"}, "days": {"message": "$DAYS$ dana", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Dodaj opcionalnu lozinku za primatelje ovog Senda.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Stvori novi Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "Nova lozinka"}, "sendDisabled": {"message": "Send <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Pravila tvrtke omogućuju brisanje samo postojećeg Senda", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send stvoren", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send je uspješno stvoren!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "Send će biti dostupan svakome s poveznicom sljedećih sat vremena.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "Send če biti dostupan svakome s poveznicom ovoliko sati: $HOURS$", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "Send će biti dostupan svakome s poveznicom 1 dan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "Send će biti dostupan svakome s poveznom ovoliko dana: $DAYS$", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Kopirana poveznica Senda", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send spremljen", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Otvori proširenje?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "Za stvar<PERSON>, potrebno je otvoriti proširenje u novi prozor.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "<PERSON>a <PERSON><PERSON><PERSON>, otvori proširenje u bočnoj traci (ako je moguće) ili u iskočnom prozoru klikom na ovu poruku."}, "sendFirefoxFileWarning": {"message": "Za odabir datoteke u Firefoxu, otvori proširenje u bočnoj traci ili otvori iskočni prozor klikom na ovau poruku."}, "sendSafariFileWarning": {"message": "Za odabir datoteke u Safariju, otvori iskočni prozor klikom na ovu poruku."}, "popOut": {"message": "Otvori"}, "sendFileCalloutHeader": {"message": "Prije početka"}, "expirationDateIsInvalid": {"message": "Navedeni rok isteka nije valjan."}, "deletionDateIsInvalid": {"message": "Navedeni datum brisanja nije valjan."}, "expirationDateAndTimeRequired": {"message": "Potrebno je unijeti datum i vrijeme isteka."}, "deletionDateAndTimeRequired": {"message": "Potrebno je unijeti datum i vrijeme brisanja."}, "dateParsingError": {"message": "Došlo je do greške kod spremanja vaših datuma isteka i brisanja."}, "hideYourEmail": {"message": "Autentifikacija"}, "passwordPrompt": {"message": "Ponovno zatraži glavnu lozinku"}, "passwordConfirmation": {"message": "Potvrda glavne lozinke"}, "passwordConfirmationDesc": {"message": "Ova radnja je zaštićena. Za nastavak i potvrdu identiteta, unesi svoju glavnu lozinku."}, "emailVerificationRequired": {"message": "Potrebna je potvrda e-pošte"}, "emailVerifiedV2": {"message": "e-pošta potvrđena"}, "emailVerificationRequiredDesc": {"message": "Moraš ovjeriti svoju e-poštu u mrežnom trezoru za koritšenje ove značajke."}, "updatedMasterPassword": {"message": "Glavna lozin<PERSON>"}, "updateMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "updateMasterPasswordWarning": {"message": "Tvoju glavnu lozinku je nedavno promijenio administrator tvoje organizacije. Za pristup trezoru, potrebno je ažurirati glavnu lozinku, što će te odjaviti iz trenutne sesije, te ćeš se morati ponovno prijaviti. Aktivne sesije na drugim uređajima mogu ostati aktivne još sat vremena."}, "updateWeakMasterPasswordWarning": {"message": "Tvoja glavna lozinka ne zadovoljava pravila ove organizacije. Za pristup trezoru moraš odmah ažurirati svoju glavnu lozinku. <PERSON><PERSON>, odjaviti ćeš se iz trenutne sesije te ćeš se morati ponovno prijaviti. Aktivne sesije na drugim uređajima mogu ostati aktivne do jedan sat."}, "tdeDisabledMasterPasswordRequired": {"message": "Tvoja je organizacija onemogućila šifriranje pouzdanog uređaja. Postavi glavnu lozinku za pristup svom trezoru."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatsko učlanjenje"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Pravilo ove organizacija automatski će te učlaniti u ponovno postavljanje lozinke. Učlanjenje će omogućiti administratorima organizacije promjenu tvoje glavne lozinke."}, "selectFolder": {"message": "Odaberi mapu..."}, "noFoldersFound": {"message": "<PERSON><PERSON> pro<PERSON> mapa", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "<PERSON><PERSON>š postaviti glavnu lozinku jer su dopuštenja tvoje organizacije ažurirana.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Tvoja organizacija zahtijeva da postaviš glavnu lozinku.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "od $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Potrebna je potvrda", "description": "Default title for the user verification dialog."}, "hours": {"message": "sat(i)"}, "minutes": {"message": "minuta"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Pravila tvrtke primijenjena su na vrijeme isteka"}, "vaultTimeoutPolicyInEffect": {"message": "Pravilo tvoje organizacije podesilo je najveće dozvoljeno vrijeme isteka trezora na $HOURS$:$MINUTES$ h.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "Pravila tvrtke primijenjena su na vrijeme isteka", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Tvoja organizacija je zadano postavila kraće vrijeme isteka. Najviše: $HOURS$:$MINUTES$", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Pravilo tvoje organizacije utječe na istek trezora. Najveće dozvoljeno vrijeme isteka je $HOURS$:$MINUTES$ h. Tvoja radnja nakon isteka trezora je: $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Pravilo tvoje organizacije podesilo je radnju nakon isteka trezora na: $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Vrijeme isteka premašuje ograničenje koje je postavila tvoja organizacija."}, "vaultExportDisabled": {"message": "<PERSON>z<PERSON>z trez<PERSON>"}, "personalVaultExportPolicyInEffect": {"message": "Jedno ili više pravila organizacija onemogućuje izvoz osobnog trezora. "}, "copyCustomFieldNameInvalidElement": {"message": "Nije moguće identificirati valjani element formulara. Pokušaj provjeriti HTML."}, "copyCustomFieldNameNotUnique": {"message": "<PERSON><PERSON> nađen jedinstveni identifikator."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Napusti organizaciju"}, "removeMasterPassword": {"message": "Ukloni glavnu lo<PERSON>"}, "removedMasterPassword": {"message": "Glavna lozinka uklonjena."}, "leaveOrganizationConfirmation": {"message": "<PERSON><PERSON><PERSON> ovu organizaciju?"}, "leftOrganization": {"message": "Organizacija napuštena."}, "toggleCharacterCount": {"message": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON> broj <PERSON>"}, "sessionTimeout": {"message": "Tvoja sesija je istekla. Vrati se i pokušaj s ponovnom prijavom."}, "exportingPersonalVaultTitle": {"message": "Izvoz osobnog trezora"}, "exportingIndividualVaultDescription": {"message": "Izvest će se samo stavke osobnog trezora povezanog s $EMAIL$. Stavke organizacijskog trezora neće biti uključene. Izvest će se samo informacija o stavci trezora bez pripadajućih podataka o povijesti lozinki i privitaka.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Izvest će se samo stavke i privici osobnog trezora povezanog s $EMAIL$. Stavke organizacijskog trezora neće biti uključene", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Izvoz organizacijskog trezora"}, "exportingOrganizationVaultDesc": {"message": "Izvest će se samo organizacijski trezor povezan s $ORGANIZATION$. Stavke iz osobnih trezora i stavke iz drugih organizacija neće biti uključene.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Pogreška"}, "decryptionError": {"message": "Pogreška pri dešifriranju"}, "couldNotDecryptVaultItemsBelow": {"message": "Bitwarden nije mogao dešifrirati sljedeće stavke trezora."}, "contactCSToAvoidDataLossPart1": {"message": "Kontaktiraj službu za korisnike", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "kako bi izbjegli gubitak podataka.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "<PERSON><PERSON><PERSON>e"}, "generateEmail": {"message": "Generiraj e-poš<PERSON>"}, "spinboxBoundariesHint": {"message": "Vrijednost mora biti u rasponu $MIN$ - $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Koristi $RECOMMENDED$ i više znakova za generiranje jake lozinke.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Koristi $RECOMMENDED$ i više riječi za generiranje jake frazne lozinke.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus adresa e-pošte", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Koristi mogućnosti podadresiranja svog davatelja e-pošte."}, "catchallEmail": {"message": "<PERSON><PERSON><PERSON> sve (catch-all) e-pošta"}, "catchallEmailDesc": {"message": "<PERSON><PERSON><PERSON> k<PERSON> catch-all sand<PERSON>čić svoje domene."}, "random": {"message": "<PERSON><PERSON><PERSON>č<PERSON>"}, "randomWord": {"message": "Nasumična riječ"}, "websiteName": {"message": "Naziv web mjesta"}, "service": {"message": "Usluga"}, "forwardedEmail": {"message": "Proslijeđeni pseudonim e-pošte"}, "forwardedEmailDesc": {"message": "<PERSON><PERSON>raj pseudonim e-pošte s vanjskom uslugom prosljeđivanja."}, "forwarderDomainName": {"message": "Domena e-pošte", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Odaberi domenu koju podržava odabrani servis", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ greška: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generirao Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Web: $WEBSITE$. Generirao Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Nevažeći $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Nevažeći $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ je odbio tvoj zahtjev. Obrati se svom pružatelju usluga za pomoć.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ je odbio tvoj zahtjev: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Nije moguće dobiti $SERVICENAME$ maskirani ID računa e-pošte.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Nevažeća $SERVICENAME$ domena.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Nevažeći $SERVICENAME$ URL.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Nepoznata $SERVICENAME$ greška.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Nepoznati prosljeditelj: '$SERVICENAME$.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Naziv poslužitelja", "description": "Part of a URL."}, "apiAccessToken": {"message": "Token za API pristup"}, "apiKey": {"message": "API ključ"}, "ssoKeyConnectorError": {"message": "Pogreška konektora ključa: provjeri je li konektor ključa dostupan i radi li ispravno."}, "premiumSubcriptionRequired": {"message": "Potrebna Premium pretplata"}, "organizationIsDisabled": {"message": "Organizacija suspendirana."}, "disabledOrganizationFilterError": {"message": "Stavkama u suspendiranoj Organizaciji se ne može pristupiti. Kontaktiraj vlasnika Organizacije za pomoć."}, "loggingInTo": {"message": "Prijava u $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Verzija poslužitelja"}, "selfHostedServer": {"message": "vlastiti poslužitelj"}, "thirdParty": {"message": "Third-party"}, "thirdPartyServerMessage": {"message": "Povezan s implementacijom poslužitelja treće strane, $SERVERNAME$. Provjeri bugove pomoću službenog poslužitelja ili ih prijavi poslužitelju treće strane.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "zadnji put viđeno: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Prijava glavnom lozinkom"}, "newAroundHere": {"message": "Novi korisnik?"}, "rememberEmail": {"message": "Zapamti adresu e-p<PERSON>š<PERSON>"}, "loginWithDevice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fingerprintPhraseHeader": {"message": "Jedinstvena fraza"}, "fingerprintMatchInfo": {"message": "Provjeri je li trezor otključan i slaže li se jedinstvena fraza s drugim uređajem."}, "resendNotification": {"message": "Ponovno pošalji obavijest"}, "viewAllLogInOptions": {"message": "Pogledaj sve mogućnosti prijave"}, "notificationSentDevice": {"message": "Obavijest je poslana na tvoj uređaj."}, "notificationSentDevicePart1": {"message": "Otključaj Bitwarden na svojem uređaju ili na"}, "notificationSentDeviceAnchor": {"message": "web trezoru"}, "notificationSentDevicePart2": {"message": "Provjeri slaže li se jedinstvena fraza s ovdje prikazanom prije odobravanja."}, "aNotificationWasSentToYourDevice": {"message": "Obavijest je poslana na tvoj uređaj"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "Dobiti ćeš obavijest kada je tvoj zahtjev odobren"}, "needAnotherOptionV1": {"message": "<PERSON><PERSON><PERSON>š drugu opciju?"}, "loginInitiated": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "logInRequestSent": {"message": "Zahtjev poslan"}, "exposedMasterPassword": {"message": "Ukradena glavna lozinka"}, "exposedMasterPasswordDesc": {"message": "Lozinka je nađena među ukradenima tijekom krađa podataka. Za zaštitu svog računa koristi jedinstvenu lozinku. <PERSON><PERSON><PERSON> li svejedno korisiti ukradenu lozinku?"}, "weakAndExposedMasterPassword": {"message": "Slaba i ukradena glavna lozinka"}, "weakAndBreachedMasterPasswordDesc": {"message": "Slaba lozinka je nađena među ukradenima tijekom krađa podataka. Za zaštitu svog računa koristi jaku i jedinstvenu lozinku. <PERSON><PERSON>š li svejedno korisiti slabu, ukradenu lozinku?"}, "checkForBreaches": {"message": "Provjeri je li lozinka ukradena prilikom krađe podataka"}, "important": {"message": "Važno:"}, "masterPasswordHint": {"message": "Glavnu lozinku nije moguće oporaviti ako ju zaboraviš!"}, "characterMinimum": {"message": "najmanje $LENGTH$ znakova", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Prema pravilima tvoje organizacije uključena je auto-ispuna prilikom učitavanja stranice."}, "howToAutofill": {"message": "Kako auto-ispuniti"}, "autofillSelectInfoWithCommand": {"message": "Odaberi stavku s ovog zaslona, upotrijebi prečac $COMMAND$ ili istraži druge opcije u postavkama.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Odaberi stavku s ovog zaslona ili istraži druge opcije u postavkama."}, "gotIt": {"message": "U redu"}, "autofillSettings": {"message": "Postavke auto-ispune"}, "autofillKeyboardShortcutSectionTitle": {"message": "Prečac auto-ispune"}, "autofillKeyboardShortcutUpdateLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Upravljaj <PERSON>"}, "autofillShortcut": {"message": "Tipkovnički precač auto-ispune"}, "autofillLoginShortcutNotSet": {"message": "Prečac auto-ispune prijave nije postavljen. Promijeni u postavkama preglednika."}, "autofillLoginShortcutText": {"message": "Prečac auto-ispune prijave je: $COMMAND$. Promijeni u postavkama preglednika.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Zadani prečac auto-ispune: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Otvara u novom prozoru"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Zapamti ovaj uređaj kako bi buduće prijave bile brže"}, "deviceApprovalRequired": {"message": "Potrebno je odobriti uređaj. Odaberi metodu odobravanja:"}, "deviceApprovalRequiredV2": {"message": "Potrebno odobrenje uređaja"}, "selectAnApprovalOptionBelow": {"message": "Odaberi opciju odobrenja"}, "rememberThisDevice": {"message": "Zapamti ovaj u<PERSON>đ<PERSON>"}, "uncheckIfPublicDevice": {"message": "Odznači ako koristiš javni uređaj"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON><PERSON>"}, "requestAdminApproval": {"message": "Zatraži odobrenje administratora"}, "ssoIdentifierRequired": {"message": "Potreban je identifikator organizacije."}, "creatingAccountOn": {"message": "Stvaranje računa na"}, "checkYourEmail": {"message": "Provjeri svoju e-poštu"}, "followTheLinkInTheEmailSentTo": {"message": "Slijedi vezu u e-pošti poslanoj na"}, "andContinueCreatingYourAccount": {"message": "za nastavak stvaranja tvojeg računa."}, "noEmail": {"message": "<PERSON>ema e<PERSON>p<PERSON>?"}, "goBack": {"message": "<PERSON><PERSON>"}, "toEditYourEmailAddress": {"message": "na uređivanje svoje adrese e-pošte."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "<PERSON><PERSON><PERSON> od<PERSON>. <PERSON><PERSON>š prava vidjeti ovu stranicu."}, "general": {"message": "<PERSON><PERSON><PERSON>"}, "display": {"message": "Prikaz"}, "accountSuccessfullyCreated": {"message": "<PERSON><PERSON><PERSON> je uspješno stvoren!"}, "adminApprovalRequested": {"message": "Zatraženo odobrenje administratora"}, "adminApprovalRequestSentToAdmins": {"message": "T<PERSON>j zahtjev je poslan administratoru."}, "troubleLoggingIn": {"message": "Problem s prijavom?"}, "loginApproved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "userEmailMissing": {"message": "Nedostaje e-pošta korisnika"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Nije pronađena e-pošta aktivnog korisnika. Odjava u tijeku..."}, "deviceTrusted": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Potreban je unos."}, "required": {"message": "obavezno"}, "search": {"message": "Traži"}, "inputMinLength": {"message": "Unos mora sadržavati najmanje $COUNT$ znakova.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Unos ne smije imati više od $COUNT$ znakova.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "Ovi znakovi nisu dozvoljeni: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Unos mora biti najmanje $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Unos ne smije biti više od $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "Jedna ili više adresa e-pošte nije valjana"}, "inputTrimValidator": {"message": "Unos ne smije biti prazan.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "<PERSON><PERSON> unesena adresa e-poš<PERSON>."}, "fieldsNeedAttention": {"message": "$COUNT$ polje/a treba tvoju pažnju.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 polje treba tvoju pažnju."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ polja treba tvoju pažnju.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Odaberi --"}, "multiSelectPlaceholder": {"message": "-- Upiši za filtriranje --"}, "multiSelectLoading": {"message": "Dohvaćanje opcija..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON> pronađena niti jedna stavka"}, "multiSelectClearAll": {"message": "Očisti sve"}, "plusNMore": {"message": "+ još $QUANTITY$", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Podizbornik"}, "toggleCollapse": {"message": "Sažmi/Proširi", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON>"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Stavke za koje je potrebna glavna lozinka neće se auto-ispuniti kod učitavanja stranice. Auto-ispuna pri učitavanju stranice je isključena.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Auto-ispuna kod učitavanja stranice koristi zadane postavke.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Isključi traženje glavne lozinke za promjenu ovog polja", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "U/Isključi bočnu navigaciju"}, "skipToContent": {"message": "Preskoči na sadržaj"}, "bitwardenOverlayButton": {"message": "Tipka izbornika Bitwarden auto-ispune", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "U/isključivanje izbornika Bitwarden auto-ispune", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Izbornik Bitwarden auto-ispune", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Otklučaj svoj račun za prikaz podudarnih prijava", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Otključaj račun za prikaz prijedloga auto-ispuna", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Otključaj račun", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Otključaj račun; otvara se u novom prozoru", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Kôd za provjeru jednokratne lozinka zasnovane na vremenu (TOTP) ", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Preostalo vrijeme koda za provjeru", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Unesi vjerodajnice za", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Djelomično korisničko ime", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Nema stavki za prikaz", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "Nova stavka", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Dodaj novu stavku trezora", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "Nova prijava", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Dodaj novu stavku prijave u trezor; otvara se u novom prozoru", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "Nova kartica", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Dodaj novu stavku kartice u trezor; otvara se u novom prozoru", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "Novi identitet", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Dodaj novu stavku identiteta u trezor; otvara se u novom prozoru", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Dostupan je Bitwarden izbornik auto-ispune. Pritisni tipku sa strelicom prema dolje za odabir.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Uključi"}, "ignore": {"message": "<PERSON><PERSON><PERSON>"}, "importData": {"message": "<PERSON><PERSON><PERSON> pod<PERSON>", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Greška prilikom uvoza"}, "importErrorDesc": {"message": "Postoji problem s podacima za uvoz. Potrebno je razriješiti doljenavedene greške u izvornoj datoteci i pokušati ponovno."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Popravi navedene greške i pokušaj ponovo."}, "description": {"message": "Opis"}, "importSuccess": {"message": "Uvoz podataka u trezor je uspio"}, "importSuccessNumberOfItems": {"message": "Ukupno je uvezeno $AMOUNT$ stavaka.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Pokušaj ponovno"}, "verificationRequiredForActionSetPinToContinue": {"message": "Za ovu radnju potrebna je potvrda. Postavi PIN za nastavak."}, "setPin": {"message": "Postavi <PERSON>"}, "verifyWithBiometrics": {"message": "Potvrdi biometrijom"}, "awaitingConfirmation": {"message": "Čekanje potvrde"}, "couldNotCompleteBiometrics": {"message": "Nije moguće dovršiti biometriju."}, "needADifferentMethod": {"message": "<PERSON><PERSON><PERSON> drugi na<PERSON>?"}, "useMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "usePin": {"message": "<PERSON><PERSON><PERSON>"}, "useBiometrics": {"message": "Koristi biometriju"}, "enterVerificationCodeSentToEmail": {"message": "Unesi kôd za potvrdu primljen e-poštom."}, "resendCode": {"message": "Ponovno pošalji kod"}, "total": {"message": "Ukupno"}, "importWarning": {"message": "Uvo<PERSON>š podatke u $ORGANIZATION$. Tvoji podaci možda će biti podijeljeni s članovima ove organizacije. <PERSON><PERSON><PERSON> li svejedno uvesti podatke?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Greška pri povezivanju s uslugom Duo. Koristi drugu metodu prijave s dvostrukom autentifikacijom ili kontaktiraj Duo za pomoć."}, "duoRequiredForAccount": {"message": "Za tvoj račun je potrebna Duo dvostruka autentifikacija."}, "popoutExtension": {"message": "Otvori proširenje"}, "launchDuo": {"message": "Pokreni Duo"}, "importFormatError": {"message": "Podaci nisu ispravno formatirani. Provjeri uvoznu datoteku i pokušaj ponovno."}, "importNothingError": {"message": "<PERSON><PERSON><PERSON> nije <PERSON>."}, "importEncKeyError": {"message": "Greška u dešifriranju izvozne datoteke. Ovaj ključ za šifriranje ne odgovara ključu za šifriranje korištenom pri izvozu datoteke."}, "invalidFilePassword": {"message": "Nesipravna lozinka datoteke. Unesi lozinku izvozne datoteke."}, "destination": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "learnAboutImportOptions": {"message": "Više o mogućnostima uvoza"}, "selectImportFolder": {"message": "Odaberi mapu"}, "selectImportCollection": {"message": "Odaberi zbirku"}, "importTargetHint": {"message": "Odaberi ovu opciju ako sadržaj uvezene datoteke želiš spremiti u $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "Datoteka sadrži nedodijeljene stavke."}, "selectFormat": {"message": "Odaberi format datoteke za uvoz"}, "selectImportFile": {"message": "Odaberi datoteku za uvoz"}, "chooseFile": {"message": "<PERSON><PERSON><PERSON><PERSON> datoteku"}, "noFileChosen": {"message": "<PERSON><PERSON> o<PERSON> da<PERSON>"}, "orCopyPasteFileContents": {"message": "ili kop<PERSON>/zalijepi sadržaj uvozne datoteke"}, "instructionsFor": {"message": "$NAME$ upute", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Potvrdi uvoz trezora"}, "confirmVaultImportDesc": {"message": "Ova je datoteka zaštićena lozinkom. Unesi lozinku za nastavak uvoza."}, "confirmFilePassword": {"message": "Potvrdi lozinku datoteke"}, "exportSuccess": {"message": "Podaci iz trezora su izvezeni"}, "typePasskey": {"message": "Pristupni ključ"}, "accessing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loggedInExclamation": {"message": "Prijava uspješna!"}, "passkeyNotCopied": {"message": "Pristupni ključ neće biti kopiran"}, "passkeyNotCopiedAlert": {"message": "Pristupni ključ se neće kopirati u kloniranu stavku. Ž<PERSON>š li nastaviti klonirati ovu stavku?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Ishodišna stranica zahtijeva verifikaciju. Ova značajka još nije implement<PERSON>na za račune bez glavne lozinke."}, "logInWithPasskeyQuestion": {"message": "Prijava pristupnim ključem?"}, "passkeyAlreadyExists": {"message": "Za ovu aplikaciju već postoji pristupni ključ."}, "noPasskeysFoundForThisApplication": {"message": "Za ovu aplikaciju nema pristupnih ključeva."}, "noMatchingPasskeyLogin": {"message": "<PERSON><PERSON> o<PERSON>e prijavu za ovu stranicu."}, "noMatchingLoginsForSite": {"message": "<PERSON>ema prijava za ovu web stranicu"}, "searchSavePasskeyNewLogin": {"message": "Potraži ili spremi pristupni ključ kao novu prijavu"}, "confirm": {"message": "Autoriziraj"}, "savePasskey": {"message": "Spremi pristupni ključ"}, "savePasskeyNewLogin": {"message": "Spremi pristupni ključ kao novu prijavu"}, "chooseCipherForPasskeySave": {"message": "Odaberi za koju prijavu želiš spremiti ovaj pristupni ključ"}, "chooseCipherForPasskeyAuth": {"message": "Odaberi pristupni ključ za prijavu"}, "passkeyItem": {"message": "Stavka pristupnog ključa"}, "overwritePasskey": {"message": "Prebriši pristupni ključ?"}, "overwritePasskeyAlert": {"message": "Ova stavka već sadrži pristupni ključ. <PERSON><PERSON><PERSON>š prebrisati trenutni pristupni ključ?"}, "featureNotSupported": {"message": "Značajka još nije podržana"}, "yourPasskeyIsLocked": {"message": "Za korištenje pristpnog ključa potrebna je autentifikacija. Potvrdi svoj identitet."}, "multifactorAuthenticationCancelled": {"message": "Multifaktorska autentifikacija otkazana"}, "noLastPassDataFound": {"message": "<PERSON><PERSON> nađeni <PERSON>ass podaci"}, "incorrectUsernameOrPassword": {"message": "Neispravno korisničko ime ili lozinka"}, "incorrectPassword": {"message": "<PERSON>eisp<PERSON>v<PERSON>"}, "incorrectCode": {"message": "Neispravan kôd"}, "incorrectPin": {"message": "Neispravan PIN"}, "multifactorAuthenticationFailed": {"message": "Multifaktorska autentifikacija nije uspjela"}, "includeSharedFolders": {"message": "Uključi dijeljene mape"}, "lastPassEmail": {"message": "LastPass e-pošta"}, "importingYourAccount": {"message": "Uvoz tvog računa..."}, "lastPassMFARequired": {"message": "Potrebna LastPass multifaktorska autenfikacija"}, "lastPassMFADesc": {"message": "Unesi svoj jednokratni kôd iz aplikacije za autentifikaciju"}, "lastPassOOBDesc": {"message": "Odobri svoj zahtjev za prijavu u svojoj aplikaciji za autentifikaciju ili unesi jednokratni kôd."}, "passcode": {"message": "Jednokratni kôd"}, "lastPassMasterPassword": {"message": "LastPass glavna lozinka"}, "lastPassAuthRequired": {"message": "Potrebna LastPass autentifikacija"}, "awaitingSSO": {"message": "Čekanje SSO autentifikacije"}, "awaitingSSODesc": {"message": "Prijavi se koristeći pristupne podatke svoje tvrtke."}, "seeDetailedInstructions": {"message": "Detaljne upute za pomoć pronađi na našoj stranici za pomoć na", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Uvezi direktno iz LastPass-a"}, "importFromCSV": {"message": "Uvezi iz CSV-a"}, "lastPassTryAgainCheckEmail": {"message": "Pokušaj ponovno ili pogledaj e-poštu od LastPass-a za potvrdu."}, "collection": {"message": "Zbirka"}, "lastPassYubikeyDesc": {"message": "Umetni YubiKey pridružen svojem LastPass računu u USB priključak račuanala, a zatim dodirni njegovu tipku."}, "switchAccount": {"message": "Promijeni račun"}, "switchAccounts": {"message": "Promijeni račune"}, "switchToAccount": {"message": "Promijeni na račun"}, "activeAccount": {"message": "Aktivni račun"}, "bitwardenAccount": {"message": "Bitwarden račun"}, "availableAccounts": {"message": "Dostupni računi"}, "accountLimitReached": {"message": "Dosegnuto je ograničenje računa. Odjavi se s računa za dodavanje sljedećeg."}, "active": {"message": "aktivan"}, "locked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unlocked": {"message": "ot<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "server": {"message": "poslužitelj"}, "hostedAt": {"message": "do<PERSON><PERSON><PERSON> na"}, "useDeviceOrHardwareKey": {"message": "Koristi svoj uređaj ili hardverski ključ"}, "justOnce": {"message": "Samo jednom"}, "alwaysForThisSite": {"message": "Uvijek za ovu stranicu"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ dodana u izuzete domene.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Uobičajeni oblici", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Nastavi na postavke preglednika?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Nastavi u centar za pomoć?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Promijeni postavke auto-ispune i upravljanja lozinkama preglednika.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "Možeš vidjeti i postaviti prečace proširenja u postavkama preglednika.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Promijeni postavke auto-ispune i upravljanja lozinkama u preglednika.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "Možeš vidjeti i postaviti prečace proširenja u postavkama preglednika.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Postavi Bitwarden kao zadani upravitelj lozinki?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ostavljanje ove postavke isključenom može uzrokovati sukob između prijedloga za auto-ispunu Bitwardena i tvojeg preglednika.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Postavi Bitwarden kao zadani upravitelj lozinki", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "<PERSON><PERSON> mog<PERSON>e postaviti Bitwarden kao zadani upravitelj lozinki", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Za postavljanje Bitwardena kao zadanog upravitelja lozinki moraš pregledniku dati dopuštenje privatnosti.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "<PERSON><PERSON> kao zadano", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Vjerodajnice uspješno spremljene!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Lozinka pohranjena!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Vjerodajnice uspješno ažurirane!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Lozinka ažurirana!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Greška pri spremanju vjerodajnica. Za detalje pogledaj konzolu.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Us<PERSON>ješno"}, "removePasskey": {"message": "Ukloni pristupni ključ"}, "passkeyRemoved": {"message": "Pristupni ključ uklonjen"}, "autofillSuggestions": {"message": "Prijedlozi auto-ispune"}, "itemSuggestions": {"message": "Predložene stavke"}, "autofillSuggestionsTip": {"message": "Spremi u auto-ispunu stavku prijave za ovu stranicu"}, "yourVaultIsEmpty": {"message": "<PERSON><PERSON><PERSON> trezor je prazan"}, "noItemsMatchSearch": {"message": "Nema stavki podudarnih s pretragom"}, "clearFiltersOrTryAnother": {"message": "Očisti filtre ili pokušaj s drugačijom pretragom"}, "copyInfoTitle": {"message": "Ko<PERSON>raj informacije - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Kopiraj <PERSON> - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "Više mogućnosti - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "Pogledaj stavku - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Auto-ispuna - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Kopiraj $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "Nema vrijednosti za kopiranje"}, "assignToCollections": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copyEmail": {"message": "Ko<PERSON>raj e-poštu"}, "copyPhone": {"message": "<PERSON><PERSON><PERSON> telefon"}, "copyAddress": {"message": "<PERSON><PERSON><PERSON>"}, "adminConsole": {"message": "Konzola administratora"}, "accountSecurity": {"message": "Sigurnost računa"}, "notifications": {"message": "Obavijesti"}, "appearance": {"message": "<PERSON><PERSON><PERSON>"}, "errorAssigningTargetCollection": {"message": "Greška pri dodjeljivanju ciljne zbirke."}, "errorAssigningTargetFolder": {"message": "Greška pri dodjeljivanju ciljne mape."}, "viewItemsIn": {"message": "Pogledaj stavku u $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Natrag na $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "Novo"}, "removeItem": {"message": "Ukloni $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Stavke bez mape"}, "itemDetails": {"message": "Detalji stavke"}, "itemName": {"message": "Naziv stavke"}, "organizationIsDeactivated": {"message": "Organizacija je deaktivirana"}, "owner": {"message": "Vlasnik"}, "selfOwnershipLabel": {"message": "Ti", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Ne može se pristupiti stavkama u deaktiviranoj Organizaciji. Kontaktiraj vlasnika Organizacije za pomoć."}, "additionalInformation": {"message": "Dodatne informacije"}, "itemHistory": {"message": "Povijest stavke"}, "lastEdited": {"message": "<PERSON>adn<PERSON>"}, "ownerYou": {"message": "Vlasnik: Ti"}, "linked": {"message": "Povezano"}, "copySuccessful": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "upload": {"message": "Prijenos"}, "addAttachment": {"message": "Dodaj privitak"}, "maxFileSizeSansPunctuation": {"message": "Najveća veličina datoteke je 500 MB"}, "deleteAttachmentName": {"message": "Izbriši privitak $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Preuzmi $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "<PERSON><PERSON><PERSON> ž<PERSON>š trajno izbrisati ovaj privitak?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Besplatne organizacije ne mogu koristiti privitke"}, "filters": {"message": "<PERSON><PERSON><PERSON>"}, "filterVault": {"message": "<PERSON><PERSON><PERSON><PERSON> trezor"}, "filterApplied": {"message": "Uključen jedan filter"}, "filterAppliedPlural": {"message": "Uključeno filtera: $COUNT$", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "<PERSON><PERSON><PERSON>ni podaci"}, "identification": {"message": "Identifikacija"}, "contactInfo": {"message": "Kontakt podaci"}, "downloadAttachment": {"message": "Preuzmi - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "broj kartice završava na", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Vjerodajnice za prijavu"}, "authenticatorKey": {"message": "Ključ autentifikatora"}, "autofillOptions": {"message": "Postavke auto-ispune"}, "websiteUri": {"message": "Web stranica (URI)"}, "websiteUriCount": {"message": "B<PERSON>j URI-ja: $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Web stranica dodana"}, "addWebsite": {"message": "Dodaj web stranicu"}, "deleteWebsite": {"message": "Izbriši web stranicu"}, "defaultLabel": {"message": "Zadano ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Prikaži otkrivanje podudaranja $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Sakrij otkrivanje podudaranja $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Auto-ispuna kod učitavanja?"}, "cardExpiredTitle": {"message": "Istekla kartica"}, "cardExpiredMessage": {"message": "<PERSON><PERSON> je o<PERSON><PERSON>, a<PERSON><PERSON><PERSON> podatke o kartici"}, "cardDetails": {"message": "Detalji kartice"}, "cardBrandDetails": {"message": "$BRAND$ detalji", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Omogući animacije"}, "showAnimations": {"message": "Prikaži animacije"}, "addAccount": {"message": "<PERSON><PERSON><PERSON>"}, "loading": {"message": "Učitavanje"}, "data": {"message": "<PERSON><PERSON><PERSON>"}, "passkeys": {"message": "Pristupni ključevi", "description": "A section header for a list of passkeys."}, "passwords": {"message": "<PERSON><PERSON><PERSON>", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Prijava pristupnim ključem", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Samo članovi organizacije s pristupom ovim zbirkama će moći vidjeti stavku."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Samo članovi organizacije s pristupom ovim zbirkama će moći vidjeti stavke."}, "bulkCollectionAssignmentWarning": {"message": "Odabrano je $TOTAL_COUNT$ stavki. Nije moguće ažurirati $READONLY_COUNT$ stavki jer nemaš dopuštenje za uređivanje.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Dodaj polje"}, "add": {"message": "<PERSON><PERSON><PERSON>"}, "fieldType": {"message": "Vrsta polja"}, "fieldLabel": {"message": "Oznaka polja"}, "textHelpText": {"message": "Koristi tekstualna polja za podatke poput sigurnosnih pitanja"}, "hiddenHelpText": {"message": "Koristi skrivena polja za osjetljive podatke poput lozinke"}, "checkBoxHelpText": {"message": "Koristi potvrdne okvire ako ih želiš auto-ispuniti u obrascu, npr. zapamti adresu e-pošte"}, "linkedHelpText": {"message": "Koristi povezano polje kada imaš problema s auto-ispunom za određenu web stranicu."}, "linkedLabelHelpText": {"message": "Unesi html id polja, naziv, aria-label ili rezervirano mjesto."}, "editField": {"message": "Uredi polje"}, "editFieldLabel": {"message": "Uredi $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Obriši $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ dodana", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Ponovno poredaj $LABEL$. Koristi tipke sa strelicom za pomicanje stavke gore ili dolje.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Ponovno poredaj URI. Koristi tipke sa strelicom za pomicanje stavke gore ili dolje."}, "reorderFieldUp": {"message": "$LABEL$ pomaknut gore, pozicija $INDEX$ od $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Odaberi zbirke za dodjelu"}, "personalItemTransferWarningSingular": {"message": "1 stavka će biti trajno prenesena u odabranu organizaciju. Više nećeš posjedovati ovu stavku."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ stavke/i će biti trajno prenesene u odabranu organizaciju. Više nećeš posjedovati ove stavke.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 stavka će biti trajno prenesena u $ORG$. Više nećeš posjedovati ovu stavku.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ stavke/i će biti trajno prenesene u $ORG$. Više nećeš posjedovati ove stavke.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Zbirke uspješno dodijeljene"}, "nothingSelected": {"message": "<PERSON>š<PERSON> nije odabrano."}, "itemsMovedToOrg": {"message": "Stavke premještene u $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Stavka premještena u $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ pomaknuto dolje, pozicija $INDEX$ od$LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Lokacija stavke"}, "fileSend": {"message": "Send datoteke"}, "fileSends": {"message": "Send datoteke"}, "textSend": {"message": "Send teksta"}, "textSends": {"message": "Send tekstovi"}, "accountActions": {"message": "Radnje na računu"}, "showNumberOfAutofillSuggestions": {"message": "Prikaži broj prijedloga auto-ispune na ikoni proširenja"}, "showQuickCopyActions": {"message": "Prikaži akcije brzog kopiranja na trezoru"}, "systemDefault": {"message": "Zadano sustavom"}, "enterprisePolicyRequirementsApplied": {"message": "Pravila tvrtke primijenjena su na ovu postavku"}, "sshPrivateKey": {"message": "Privatni ključ"}, "sshPublicKey": {"message": "Javni ključ"}, "sshFingerprint": {"message": "Otisak prsta"}, "sshKeyAlgorithm": {"message": "Vrsta ključa"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Pokušaj ponovo"}, "vaultCustomTimeoutMinimum": {"message": "Najmanje prilagođeno vrijeme je 1 minuta."}, "additionalContentAvailable": {"message": "Dost<PERSON>an je dodatni sad<PERSON>"}, "fileSavedToDevice": {"message": "Datoteka spremljena na uređaj. Upravljaj u preuzimanjima svog uređaja."}, "showCharacterCount": {"message": "P<PERSON><PERSON><PERSON> broj <PERSON>"}, "hideCharacterCount": {"message": "<PERSON><PERSON><PERSON><PERSON> broj <PERSON>"}, "itemsInTrash": {"message": "Stavke u smeću"}, "noItemsInTrash": {"message": "Nema stavki u smeću"}, "noItemsInTrashDesc": {"message": "Stavke koje obrišeš biti će premještene ovdje, a nakon 30 dana biti će trajno izbrisane"}, "trashWarning": {"message": "Stavke koje se nalaze u Smeću duže od 30 dana će biti automatski izbrisane"}, "restore": {"message": "<PERSON><PERSON><PERSON>"}, "deleteForever": {"message": "Izbriši zauvijek"}, "noEditPermissions": {"message": "Nemaš prava za uređivanje ove stavke"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometrijsko otključavanje nije dostupno jer je prvo potrebno otključati PIN-om ili lozinkom."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometrijsko otključavanje trenutno nije dostupno."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometrijsko otključavanje nije dostupno zbog pogrešno konfiguriranih sistemskih datoteka."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometrijsko otključavanje nije dostupno zbog pogrešno konfiguriranih sistemskih datoteka."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometrijsko otključavanje nije dostupno jer je Bitwarden dekstop aplikacija zatvorena."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometrijsko otključavanje nije dostupno jer nije omogućeno za $EMAIL$ u Bitwarden desktop aplikaciji.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometrijsko otključavanje trenutno nije dostupno iz nepoznatog razloga."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Autentifikacija"}, "fillGeneratedPassword": {"message": "Ispuni generiranu lozinku", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "<PERSON><PERSON><PERSON> re-generirana", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Razmak", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "znak ˜", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "znak `", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "znak !", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "znak @", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "znak #", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "znak $", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "znak %", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "znak ^", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "znak &", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "znak *", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "lijeva zagrada (", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "<PERSON>na zag<PERSON> )", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "donja crtica _", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "crtica -", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "znak +", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "znak =", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "znak {", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "znak }", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "znak [", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "zank ]", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "znak |", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "znak \\", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "znak :", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "znak ;", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "znak \"", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "znak '", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "znak <", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "znak >", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "znak ,", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "znak .", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "znak ?", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "znak /", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Mala slova"}, "uppercaseAriaLabel": {"message": "Velika slova"}, "generatedPassword": {"message": "<PERSON><PERSON><PERSON>"}, "compactMode": {"message": "Kompaktni način"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "<PERSON><PERSON><PERSON>"}, "wide": {"message": "<PERSON><PERSON><PERSON>"}, "extraWide": {"message": "Ekstra široko"}, "sshKeyWrongPassword": {"message": "Unesena lozinka nije ispravna."}, "importSshKey": {"message": "Uvoz"}, "confirmSshKeyPassword": {"message": "Potvrdi lozinku"}, "enterSshKeyPasswordDesc": {"message": "Unesi lozinku za SSH ključ."}, "enterSshKeyPassword": {"message": "<PERSON><PERSON> lozinku"}, "invalidSshKey": {"message": "SSH ključ nije valjan"}, "sshKeyTypeUnsupported": {"message": "Tip SSH ključa nije podržan"}, "importSshKeyFromClipboard": {"message": "Uvezi ključ iz međuspremnika"}, "sshKeyImported": {"message": "SSH ključ uspješno uvezen"}, "cannotRemoveViewOnlyCollections": {"message": "S dopuštenjima samo za prikaz ne možeš ukloniti zbirke: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "<PERSON><PERSON><PERSON>, ažuriraj svoju desktop aplikaciju"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "Za korištenje biometrijskog otključavanja ažuriraj desktop aplikaciju ili nemogući otključavanje otiskom prsta u desktop aplikaciji."}, "changeAtRiskPassword": {"message": "Promijeni rizičnu lozinku"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}