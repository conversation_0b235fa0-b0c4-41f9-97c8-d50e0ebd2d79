# BitTOTP-Free 未来更新策略

## 概述
本文档制定了 BitTOTP-Free 项目的标准化更新流程，用于应对 Bitwarden 官方客户端的版本更新，确保 TOTP 解锁功能持续可用。

## 更新检测机制

### 1. 自动化监控
- **GitHub Actions 工作流**: `check-upstream-updates.yml` 每日自动检查
- **监控目标**: Bitwarden 官方仓库的新版本标签
- **触发条件**: 发现新的 `browser-v*` 标签时自动创建 Issue

### 2. 关键文件监控
系统会自动监控以下包含 TOTP 修改的关键文件：
```
libs/common/src/billing/services/account/billing-account-profile-state.service.ts
libs/vault/src/services/copy-cipher-field.service.ts
libs/vault/src/cipher-view/login-credentials/login-credentials-view.component.html
libs/angular/src/vault/components/view.component.ts
apps/browser/src/autofill/services/autofill.service.ts
```

### 3. 变更分析
- **自动对比**: 检测关键文件的代码变更
- **风险评估**: 区分安全更新和需要手动审查的更新
- **报告生成**: 自动生成详细的变更分析报告

## 标准化更新流程

### Phase 1: 更新检测和评估
1. **接收更新通知**
   - 查看自动创建的 GitHub Issue
   - 评估更新的紧急程度和影响范围

2. **初步分析**
   ```bash
   # 获取上游更新
   git remote add upstream https://github.com/bitwarden/clients.git
   git fetch upstream
   
   # 查看版本差异
   git log --oneline HEAD..upstream/main
   ```

3. **关键文件检查**
   ```bash
   # 检查关键文件变更
   git diff HEAD upstream/main -- libs/common/src/billing/services/account/billing-account-profile-state.service.ts
   git diff HEAD upstream/main -- libs/vault/src/services/copy-cipher-field.service.ts
   # ... 其他关键文件
   ```

### Phase 2: 更新策略决策
根据变更类型选择更新策略：

#### 策略 A: 安全快速更新 (无关键文件变更)
- **适用**: 关键文件无变更，仅有安全修复或其他改进
- **流程**: 直接合并上游更新，验证构建

#### 策略 B: 谨慎更新 (关键文件有变更)
- **适用**: 关键文件有变更，需要重新应用 TOTP 修改
- **流程**: 详细分析变更，更新修改，全面测试

#### 策略 C: 延迟更新 (重大架构变更)
- **适用**: 大规模重构或架构变更
- **流程**: 深入研究，可能需要重新设计修改方案

### Phase 3: 执行更新

#### 3.1 创建更新分支
```bash
# 创建更新分支
git checkout -b update-to-v$(date +%Y.%m.%d)

# 合并上游更新
git merge upstream/main
```

#### 3.2 重新应用 TOTP 修改
使用 `TOTP_MODIFICATION_GUIDE.md` 中的详细指南：

1. **检查修改完整性**
   ```bash
   # 运行验证脚本
   bash scripts/verify-modifications.sh
   ```

2. **重新应用必要的修改**
   - 参考修改指南中的具体代码变更
   - 适应新版本的代码结构变化

3. **验证修改效果**
   ```bash
   # 检查所有修改是否正确应用
   grep -r "// MODIFIED:" libs/ apps/
   ```

#### 3.3 测试验证
```bash
# 安装依赖
npm install --force

# 构建测试
cd apps/browser
npm run build:prod:chrome

# 功能测试
# 1. 安装构建的扩展
# 2. 登录 Bitwarden 账户
# 3. 验证 TOTP 功能是否正常工作
```

### Phase 4: 发布更新

#### 4.1 创建发布
```bash
# 推送更新分支
git push origin update-to-v$(date +%Y.%m.%d)

# 创建 Pull Request 进行代码审查

# 合并到主分支后创建标签
git tag v$(date +%Y.%m.%d)
git push origin v$(date +%Y.%m.%d)
```

#### 4.2 自动化发布
- GitHub Actions 会自动构建多浏览器版本
- 自动创建 GitHub Release
- 生成更新说明和下载链接

## 常见更新模式分析

### 1. 文件重命名或移动
**检测方法**:
```bash
git log --follow --name-status upstream/main -- [原文件路径]
```

**应对策略**:
- 更新 `TOTP_MODIFICATION_GUIDE.md` 中的文件路径
- 更新 GitHub Actions 中的验证脚本
- 重新应用修改到新位置

### 2. 函数签名变化
**检测方法**:
```bash
git diff HEAD upstream/main -- [文件路径] | grep -A5 -B5 "function\|method"
```

**应对策略**:
- 分析新的函数签名
- 调整修改代码以适应新签名
- 更新修改指南中的示例代码

### 3. 新增付费检查点
**检测方法**:
```bash
git diff HEAD upstream/main | grep -i "premium\|billing\|subscription"
```

**应对策略**:
- 识别新的付费检查逻辑
- 添加相应的绕过代码
- 更新修改指南和验证脚本

### 4. UI 框架升级
**检测方法**:
```bash
git diff HEAD upstream/main -- package.json | grep -i "angular\|@angular"
```

**应对策略**:
- 检查模板语法变化
- 更新 HTML 模板中的修改
- 测试 UI 功能完整性

## Git 分支策略

### 分支结构
```
main                    # 稳定的修改版本
├── update-to-v2025.7.1 # 更新分支
├── hotfix-totp-issue   # 紧急修复分支
└── experimental       # 实验性功能分支
```

### 分支管理规则
1. **main 分支**: 始终保持稳定可用
2. **更新分支**: 用于集成上游更新
3. **修复分支**: 用于紧急问题修复
4. **实验分支**: 用于测试新的修改方案

## 自动化检测脚本

### 1. 修改验证脚本
创建 `scripts/verify-modifications.sh`:
```bash
#!/bin/bash
# 验证所有 TOTP 修改是否正确应用

echo "验证 TOTP 修改完整性..."

# 检查所有必需的修改标记
required_modifications=(
    "// Always return true to enable TOTP for all users"
    "// MODIFIED: Simply check if cipher has TOTP, bypass premium restrictions"
    "<!-- MODIFIED: Removed premium badge requirement -->"
    "// MODIFIED: Never show premium required for TOTP"
    "// MODIFIED: Removed premium restriction for TOTP"
)

missing_count=0
for mod in "${required_modifications[@]}"; do
    if ! grep -r "$mod" libs/ apps/ > /dev/null; then
        echo "❌ 缺失修改: $mod"
        ((missing_count++))
    else
        echo "✅ 找到修改: $mod"
    fi
done

if [ $missing_count -eq 0 ]; then
    echo "✅ 所有 TOTP 修改验证通过"
    exit 0
else
    echo "❌ $missing_count 个修改缺失"
    exit 1
fi
```

### 2. 上游变更检测脚本
创建 `scripts/check-upstream-changes.sh`:
```bash
#!/bin/bash
# 检测上游关键文件变更

echo "检测上游关键文件变更..."

key_files=(
    "libs/common/src/billing/services/account/billing-account-profile-state.service.ts"
    "libs/vault/src/services/copy-cipher-field.service.ts"
    "libs/vault/src/cipher-view/login-credentials/login-credentials-view.component.html"
    "libs/angular/src/vault/components/view.component.ts"
    "apps/browser/src/autofill/services/autofill.service.ts"
)

git fetch upstream
changed_files=()

for file in "${key_files[@]}"; do
    if git diff --quiet HEAD upstream/main -- "$file"; then
        echo "✅ 无变更: $file"
    else
        echo "⚠️  有变更: $file"
        changed_files+=("$file")
    fi
done

if [ ${#changed_files[@]} -eq 0 ]; then
    echo "✅ 所有关键文件无变更，可以安全更新"
    exit 0
else
    echo "⚠️  ${#changed_files[@]} 个关键文件有变更，需要手动审查"
    exit 1
fi
```

## 回退方案

### 1. 快速回退
如果更新后发现问题：
```bash
# 回退到上一个稳定版本
git checkout main
git reset --hard [上一个稳定提交]
git push --force-with-lease origin main
```

### 2. 选择性回退
如果只有部分功能有问题：
```bash
# 回退特定文件
git checkout HEAD~1 -- [有问题的文件]
git commit -m "Revert problematic changes in [文件名]"
```

### 3. 紧急修复
```bash
# 创建紧急修复分支
git checkout -b hotfix-urgent-issue

# 应用修复
# ... 修复代码 ...

# 快速发布
git tag v$(date +%Y.%m.%d)-hotfix
git push origin v$(date +%Y.%m.%d)-hotfix
```

## 质量保证检查清单

### 更新前检查
- [ ] 备份当前稳定版本
- [ ] 确认上游更新的变更范围
- [ ] 评估更新的紧急程度

### 更新过程检查
- [ ] 所有 TOTP 修改已重新应用
- [ ] 修改验证脚本通过
- [ ] 构建过程无错误
- [ ] 基本功能测试通过

### 更新后检查
- [ ] TOTP 功能完全正常
- [ ] 扩展可以正常安装和运行
- [ ] 没有新的付费限制出现
- [ ] 用户界面显示正确

### 发布前检查
- [ ] 版本号正确更新
- [ ] 发布说明准确完整
- [ ] 下载文件完整可用
- [ ] 文档已相应更新

## 监控和维护

### 1. 定期检查
- **每周**: 检查自动化工作流状态
- **每月**: 手动验证 TOTP 功能
- **每季度**: 评估和优化更新流程

### 2. 社区反馈
- 监控 GitHub Issues 中的用户反馈
- 及时响应功能问题报告
- 收集改进建议

### 3. 文档维护
- 保持修改指南的准确性
- 更新更新策略文档
- 维护故障排除指南

---
**文档版本**: 1.0  
**最后更新**: 2025-07-02  
**维护者**: BitTOTP-Free 项目团队
