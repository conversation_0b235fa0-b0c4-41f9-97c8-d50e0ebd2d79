<popup-page>
  <popup-header
    slot="header"
    [pageTitle]="headerText"
    [backAction]="handleBackButton"
    showBackButton
  >
    <app-pop-out slot="end" />
  </popup-header>

  <vault-cipher-form
    *ngIf="!loading"
    formId="cipherForm"
    [config]="config"
    (cipherSaved)="onCipherSaved($event)"
    [beforeSubmit]="checkFido2UserVerification"
    [submitBtn]="submitBtn"
  >
    <app-open-attachments
      slot="attachment-button"
      [cipherId]="originalCipherId"
    ></app-open-attachments>
  </vault-cipher-form>

  <popup-footer slot="footer">
    <button bitButton type="submit" form="cipherForm" buttonType="primary" #submitBtn>
      {{ "save" | i18n }}
    </button>

    <button (click)="handleBackButton()" bitButton type="button" buttonType="secondary">
      {{ "cancel" | i18n }}
    </button>

    <button
      slot="end"
      *ngIf="canDeleteCipher$ | async"
      [bitAction]="delete"
      type="button"
      buttonType="danger"
      bitIconButton="bwi-trash"
      [appA11yTitle]="'delete' | i18n"
    ></button>
  </popup-footer>
</popup-page>
