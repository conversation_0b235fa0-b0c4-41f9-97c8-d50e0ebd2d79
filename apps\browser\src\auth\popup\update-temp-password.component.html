<form #form (ngSubmit)="submit()" [appApiAction]="formPromise">
  <header>
    <div class="left">
      <button type="button" (click)="logOut()">{{ "logOut" | i18n }}</button>
    </div>
    <h1 class="center">
      <span class="title">{{ "updateMasterPassword" | i18n }}</span>
    </h1>
    <div class="right">
      <button type="submit" [disabled]="form.loading">
        <span [hidden]="form.loading">{{ "submit" | i18n }}</span>
        <i class="bwi bwi-spinner bwi-lg bwi-spin" [hidden]="!form.loading" aria-hidden="true"></i>
      </button>
    </div>
  </header>
  <main tabindex="-1">
    <app-callout type="warning" title="{{ 'updateMasterPassword' | i18n }}">
      {{ masterPasswordWarningText }}
    </app-callout>
    <app-callout
      type="info"
      [enforcedPolicyOptions]="enforcedPolicyOptions"
      *ngIf="enforcedPolicyOptions"
    >
    </app-callout>
    <div class="box" *ngIf="requireCurrentPassword">
      <div class="box-content">
        <div class="box-content-row" appBoxRow>
          <div class="box-content-row-flex">
            <div class="row-main">
              <label for="currentMasterPassword">
                {{ "currentMasterPass" | i18n }}
              </label>
              <input
                id="currentMasterPassword"
                type="password"
                name="CurrentMasterPassword"
                class="monospaced"
                [(ngModel)]="verification.secret"
                required
                appInputVerbatim
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="box-content">
        <div class="box-content-row" appBoxRow>
          <div class="box-content-row-flex">
            <div class="row-main">
              <label for="masterPassword">
                {{ "newMasterPass" | i18n }}
                <strong class="sub-label text-{{ color }}" *ngIf="text">
                  {{ text }}
                </strong>
              </label>
              <input
                id="masterPassword"
                type="{{ showPassword ? 'text' : 'password' }}"
                name="MasterPassword"
                class="monospaced"
                [(ngModel)]="masterPassword"
                required
                appInputVerbatim
              />
            </div>
            <div class="action-buttons">
              <button
                type="button"
                class="row-btn"
                appStopClick
                appA11yTitle="{{ 'toggleVisibility' | i18n }}"
                (click)="togglePassword(false)"
                [attr.aria-pressed]="showPassword"
              >
                <i
                  class="bwi bwi-lg"
                  aria-hidden="true"
                  [ngClass]="{ 'bwi-eye': !showPassword, 'bwi-eye-slash': showPassword }"
                ></i>
              </button>
            </div>
          </div>
          <app-password-strength
            [password]="masterPassword"
            [email]="email"
            (passwordStrengthResult)="getStrengthResult($event)"
            (passwordScoreColor)="getPasswordScoreText($event)"
          >
          </app-password-strength>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="box-content">
        <div class="box-content-row box-content-row-flex" appBoxRow>
          <div class="row-main">
            <label for="masterPasswordRetype">{{ "confirmNewMasterPass" | i18n }}</label>
            <input
              id="masterPasswordRetype"
              type="{{ showPassword ? 'text' : 'password' }}"
              name="MasterPasswordRetype"
              class="monospaced"
              [(ngModel)]="masterPasswordRetype"
              required
              appInputVerbatim
            />
          </div>
          <div class="action-buttons">
            <button
              type="button"
              class="row-btn"
              appStopClick
              appA11yTitle="{{ 'toggleVisibility' | i18n }}"
              (click)="togglePassword(true)"
              [attr.aria-pressed]="showPassword"
            >
              <i
                class="bwi bwi-lg"
                aria-hidden="true"
                [ngClass]="{ 'bwi-eye': !showPassword, 'bwi-eye-slash': showPassword }"
              ></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="box-content">
        <div class="box-content-row" appBoxRow>
          <label for="hint">{{ "masterPassHint" | i18n }}</label>
          <input id="hint" type="text" name="Hint" aria-describedby="hintHelp" [(ngModel)]="hint" />
        </div>
      </div>
      <div id="hintHelp" class="box-footer">
        {{ "masterPassHintDesc" | i18n }}
      </div>
    </div>
  </main>
</form>
