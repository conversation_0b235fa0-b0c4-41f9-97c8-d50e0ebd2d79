<popup-page [loading]="sendsLoading$ | async">
  <popup-header slot="header" [pageTitle]="'send' | i18n">
    <ng-container slot="end">
      <tools-new-send-dropdown *ngIf="!sendsDisabled"></tools-new-send-dropdown>
      <app-pop-out></app-pop-out>
      <app-current-account></app-current-account>
    </ng-container>
  </popup-header>
  <ng-container slot="above-scroll-area" *ngIf="!(sendsLoading$ | async)">
    <bit-callout *ngIf="sendsDisabled" [title]="'sendDisabled' | i18n">
      {{ "sendDisabledWarning" | i18n }}
    </bit-callout>
    <ng-container *ngIf="listState !== sendState.Empty">
      <tools-send-search></tools-send-search>
      <app-send-list-filters></app-send-list-filters>
    </ng-container>
  </ng-container>

  <div
    *ngIf="listState === sendState.Empty"
    class="tw-flex tw-flex-col tw-h-full tw-justify-center"
  >
    <bit-no-items [icon]="noItemIcon" class="tw-text-main">
      <ng-container slot="title">{{ "sendsTitleNoItems" | i18n }}</ng-container>
      <ng-container slot="description">{{ "sendsBodyNoItems" | i18n }}</ng-container>
      <tools-new-send-dropdown
        [hideIcon]="true"
        *ngIf="!sendsDisabled"
        slot="button"
        [buttonType]="'secondary'"
      ></tools-new-send-dropdown>
    </bit-no-items>
  </div>

  <ng-container *ngIf="listState !== sendState.Empty">
    <div
      *ngIf="listState === sendState.NoResults"
      class="tw-flex tw-flex-col tw-justify-center tw-h-auto tw-pt-12"
    >
      <bit-no-items [icon]="noResultsIcon">
        <ng-container slot="title">{{ "noItemsMatchSearch" | i18n }}</ng-container>
        <ng-container slot="description">{{ "clearFiltersOrTryAnother" | i18n }}</ng-container>
      </bit-no-items>
    </div>
    <app-send-list-items-container [headerText]="title | i18n" [sends]="sends$ | async" />
  </ng-container>
</popup-page>
