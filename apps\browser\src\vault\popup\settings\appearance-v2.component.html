<popup-page [loading]="formLoading">
  <popup-header slot="header" [pageTitle]="'appearance' | i18n" showBackButton>
    <ng-container slot="end">
      <app-pop-out></app-pop-out>
    </ng-container>
  </popup-header>

  <form [formGroup]="appearanceForm">
    <bit-card>
      <bit-form-field>
        <bit-label>{{ "theme" | i18n }}</bit-label>
        <bit-select formControlName="theme">
          <bit-option
            *ngFor="let o of themeOptions"
            [value]="o.value"
            [label]="o.name"
          ></bit-option>
        </bit-select>
      </bit-form-field>

      <bit-form-field>
        <bit-label>{{ "extensionWidth" | i18n }}</bit-label>
        <bit-select formControlName="width" [items]="widthOptions"></bit-select>
      </bit-form-field>

      <bit-form-control>
        <input bitCheckbox formControlName="enableCompactMode" type="checkbox" />
        <bit-label
          >{{ "compactMode" | i18n }}
          <span bitBadge variant="warning">{{ "beta" | i18n }}</span></bit-label
        >
      </bit-form-control>

      <bit-form-control>
        <input bitCheckbox formControlName="enableBadgeCounter" type="checkbox" />
        <bit-label>{{ "showNumberOfAutofillSuggestions" | i18n }}</bit-label>
      </bit-form-control>

      <bit-form-control disableMargin>
        <input bitCheckbox formControlName="enableAnimations" type="checkbox" />
        <bit-label>{{ "showAnimations" | i18n }}</bit-label>
      </bit-form-control>
    </bit-card>
    <h2 bitTypography="h6" class="tw-font-bold tw-mt-4">{{ "vaultCustomization" | i18n }}</h2>
    <bit-card>
      <bit-form-control>
        <input bitCheckbox formControlName="enableFavicon" type="checkbox" />
        <bit-label>{{ "enableFavicon" | i18n }}</bit-label>
      </bit-form-control>
      <bit-form-control>
        <input bitCheckbox formControlName="showQuickCopyActions" type="checkbox" />
        <bit-label>{{ "showQuickCopyActions" | i18n }}</bit-label>
      </bit-form-control>
      <bit-form-control disableMargin>
        <input bitCheckbox formControlName="clickItemsToAutofillVaultView" type="checkbox" />
        <bit-label>
          {{ "clickToAutofill" | i18n }}
        </bit-label>
      </bit-form-control>
    </bit-card>
  </form>
</popup-page>
